<template>
  <div class="task-flow-editor">
    <!-- 任务流编辑部分 -->
    <div class="section-header">
      <span class="section-title">任务流编辑</span>
      <div class="header-controls">
        <button class="task-management-btn" @click="openTaskManagement">
          <i class="el-icon-setting"></i>
          任务配置
        </button>
      </div>
    </div>

    <div class="section-content">
      <div class="flow-editor-container">
        <!-- 动态高度待选区布局 -->
        <div class="stencil-wrapper" :id="unifiedStencilId" :class="stencilHeightClass"></div>
        <div ref="graphContainer" id="graph-container" class="graph-container">
        </div>
        <!-- 浮动操作提示 -->
        <div class="help-tips">
          <div class="tip-item">Shift+拖拽：移动画布</div>
          <div class="tip-item">Ctrl+滚轮：缩放</div>
          <div class="tip-item">Backspace：删除</div>
          <div class="tip-item">双击节点/连线：编辑属性</div>
        </div>
      </div>
    </div>

    <!-- 任务管理对话框 -->
    <div v-if="showTaskManagement" class="dialog-overlay" @click.self="closeTaskManagement">
      <div class="dialog task-management-dialog" @click.stop>
        <h3>任务管理</h3>
        <div class="dialog-body">
          <!-- 计算任务管理 -->
          <div class="task-management-content">
              <div class="section-header-mini">
                <span class="section-title-mini">计算任务列表</span>
                <button class="add-btn-mini" @click="addNewTask">
                  <i class="el-icon-plus"></i>
                  添加任务
                </button>
              </div>

              <el-table
                v-if="taskList.length > 0"
                :data="taskList"
                :header-cell-style="{whiteSpace: 'nowrap', padding: '8px 12px'}"
                border
                class="center-table"
                style="width: 100%"
              >
                <el-table-column label="序号" min-width="80" show-overflow-tooltip>
                  <template slot-scope="scope">
                    J{{ scope.$index }}
                  </template>
                </el-table-column>
                <el-table-column label="任务名称" min-width="150" prop="name" show-overflow-tooltip></el-table-column>
                <el-table-column label="计算中心" min-width="180" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ scope.row.c2netJobSpecification.mandatoryComputingCenterName }}
                  </template>
                </el-table-column>
                <el-table-column label="节点数" min-width="80" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ scope.row.c2netJobSpecification.nNodes }}
                  </template>
                </el-table-column>
                <el-table-column label="加速卡" min-width="120" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ scope.row.c2netJobSpecification.nodeSpecification.graphicsCardType }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="120">
                  <template slot-scope="scope">
                    <el-button type="text" @click="editTask(scope.row, scope.$index)">
                      <i class="el-icon-edit"></i> 编辑
                    </el-button>
                    <el-button type="text" @click="deleteTask(scope.$index)">
                      <i class="el-icon-delete"></i> 删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <div v-else class="empty-table">
                <span>暂无计算任务，请点击"添加任务"按钮添加</span>
              </div>
          </div>
        </div>
        <div class="dialog-actions">
          <button @click="closeTaskManagement" type="button">
            <i class="el-icon-check"></i>
            完成
          </button>
        </div>
      </div>
    </div>



    <!-- 添加/编辑任务对话框 -->
    <div v-if="showAddTaskForm" class="dialog-overlay" @click.self="closeTaskForm">
      <div class="dialog" @click.stop>
        <h3>{{ editingTaskIndex === -1 ? '添加任务' : '编辑任务' }}</h3>

        <el-form ref="taskForm" :model="taskForm" :rules="taskRules" class="form-container" label-suffix=":" label-width="160px">
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 基本信息 -->
              <div class="form-section-title">基本信息</div>

              <el-form-item label="指定计算中心" prop="c2netJobSpecification.mandatoryComputingCenterName">
                <el-select
                  v-model="taskForm.c2netJobSpecification.mandatoryComputingCenterName"
                  class="fixed-width"
                  filterable
                  :placeholder="computingCenters.length > 0 ? '请选择计算中心' : '请先添加计算中心'"
                  @visible-change="handleComputingCenterSelectOpen"
                  @change="handleComputingCenterChange">
                  <el-option
                    v-for="center in computingCenters"
                    :key="center.id || center.name"
                    :label="center.name"
                    :value="center.name">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="节点数量" prop="c2netJobSpecification.nNodes">
                <el-input
                  v-model.number="taskForm.c2netJobSpecification.nNodes"
                  :max="1000"
                  :min="1"
                  class="fixed-width"
                  type="number"
                  @keydown="validateNumberInput">
                </el-input>
                <span class="unit-label">个</span>
              </el-form-item>

              <!-- 硬件配置 -->
              <div class="form-section-title">硬件配置</div>

              <el-form-item label="加速卡型号" prop="c2netJobSpecification.nodeSpecification.graphicsCardType">
                <el-select v-model="taskForm.c2netJobSpecification.nodeSpecification.graphicsCardType" class="fixed-width" placeholder="请选择加速卡型号" disabled>
                  <el-option value="ASCEND910" label="ASCEND910"></el-option>
                  <el-option value="A100" label="A100"></el-option>
                  <el-option value="V100" label="V100"></el-option>
                  <el-option value="ENFLAME-T20" label="ENFLAME-T20"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="加速卡数/节点" prop="c2netJobSpecification.nodeSpecification.nGraphicsCards">
                <el-input
                  v-model.number="taskForm.c2netJobSpecification.nodeSpecification.nGraphicsCards"
                  :max="8"
                  :min="1"
                  class="fixed-width"
                  type="number"
                  @keydown="validateNumberInput">
                </el-input>
                <span class="unit-label">个</span>
              </el-form-item>

              <el-form-item label="显存/卡" prop="c2netJobSpecification.nodeSpecification.nGBMemoriesPerGraphicsCard">
                <el-input
                  v-model.number="taskForm.c2netJobSpecification.nodeSpecification.nGBMemoriesPerGraphicsCard"
                  :max="100"
                  :min="1"
                  class="fixed-width"
                  type="number"
                  disabled
                  @keydown="validateNumberInput">
                </el-input>
                <span class="unit-label">GB</span>
              </el-form-item>

              <el-form-item label="CPUs/节点" prop="c2netJobSpecification.nodeSpecification.nCpus">
                <el-input
                  v-model.number="taskForm.c2netJobSpecification.nodeSpecification.nCpus"
                  :max="1024"
                  :min="1"
                  class="fixed-width"
                  type="number"
                  disabled
                  @keydown="validateNumberInput">
                </el-input>
                <span class="unit-label">核</span>
              </el-form-item>

              <el-form-item label="内存/节点" prop="c2netJobSpecification.nodeSpecification.nGBMemories">
                <el-input
                  v-model.number="taskForm.c2netJobSpecification.nodeSpecification.nGBMemories"
                  :max="2000"
                  :min="1"
                  class="fixed-width"
                  type="number"
                  disabled
                  @keydown="validateNumberInput">
                </el-input>
                <span class="unit-label">GB</span>
              </el-form-item>

              <!-- 数据集配置 -->
              <div class="form-section-title">数据集配置</div>

              <el-form-item label="数据集大小" prop="c2netJobSpecification.datasetSpecification.nMDataset">
                <el-input
                  v-model.number="taskForm.c2netJobSpecification.datasetSpecification.nMDataset"
                  :min="1"
                  class="fixed-width"
                  type="number"
                  disabled
                  @keydown="validateNumberInput">
                </el-input>
                <span class="unit-label">MB</span>
              </el-form-item>

              <el-form-item label="源中心ID" prop="c2netJobSpecification.datasetSpecification.srcCenterId">
                <el-input
                  v-model.number="taskForm.c2netJobSpecification.datasetSpecification.srcCenterId"
                  class="fixed-width"
                  type="number"
                  disabled
                  placeholder="-1表示自动分配">
                </el-input>
              </el-form-item>

            </el-col>
          </el-row>
        </el-form>

        <div class="dialog-actions">
          <button @click="saveTask">{{ editingTaskIndex === -1 ? '添加' : '保存' }}</button>
          <button @click="closeTaskForm">取消</button>
        </div>
      </div>
    </div>

    <!-- 节点属性编辑对话框 -->
    <div v-if="showNodePropertiesDialog" class="dialog-overlay" @click.self="closeNodePropertiesDialog">
      <div class="dialog node-properties-dialog" @click.stop>
        <h3>
          <i :class="currentEditingNode && currentEditingNode.shape === 'comm-rect' ? 'el-icon-connection' : 'el-icon-cpu'"></i>
          {{ currentEditingNode ? currentEditingNode.getLabel() : '' }} - 节点属性
        </h3>

        <div class="dialog-body">
          <!-- 通信节点属性编辑 -->
          <div v-if="currentEditingNode && currentEditingNode.shape === 'comm-rect'" class="properties-form">
            <div class="form-section">
              <h4>集体通信规格</h4>
              <div class="form-group">
                <label>通信类型名称：</label>
                <input
                  v-model="editingProperties.name"
                  placeholder="如：allreduce"
                  readonly
                  class="readonly-input"
                />
              </div>
              <div class="form-group">
                <label>通信算法：</label>
                <select v-model="editingProperties.algorithm" disabled class="readonly-select">
                  <option value="ring">ring</option>
                </select>
              </div>
            </div>

            <div class="form-section">
              <h4>复制的集体通信规格</h4>
              <div class="form-group">
                <label>组内ID：</label>
                <input
                  type="number"
                  v-model.number="editingProperties.idInGroup"
                  readonly
                  class="readonly-input"
                />
              </div>
              <div class="form-group">
                <label>数据集大小(MB)：</label>
                <input
                  type="number"
                  v-model.number="editingProperties.nMDataset"
                />
              </div>
            </div>
          </div>

          <!-- 算力中心属性编辑 -->
          <div v-if="currentEditingNode && currentEditingNode.shape === 'compute-rect'" class="properties-form">
            <div class="form-section">
              <h4>计算配置</h4>
              <div class="form-group">
                <label>运行时间(秒)：</label>
                <input
                  type="number"
                  v-model.number="editingProperties.runDurationTime"
                />
              </div>
              <div class="form-group">
                <label>前置任务：</label>
                <el-select
                  v-model="editingProperties.prerequisiteTasks"
                  multiple
                  placeholder="请选择前置任务"
                  style="width: 100%"
                  clearable
                  size="small"
                  class="prerequisite-select"
                >
                  <el-option
                    v-for="task in availablePrerequisiteTasks"
                    :key="task.id"
                    :label="task.label"
                    :value="task.id"
                    class="prerequisite-option"
                  />
                </el-select>
              </div>
            </div>
          </div>
        </div>

        <div class="dialog-actions">
          <button @click="saveNodeProperties">
            <i class="el-icon-check"></i>
            保存
          </button>
          <button @click="closeNodePropertiesDialog">
            <i class="el-icon-close"></i>
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- 边属性编辑对话框 -->
    <div v-if="showEdgePropertiesDialog" class="dialog-overlay" @click.self="closeEdgePropertiesDialog">
      <div class="dialog edge-properties-dialog" @click.stop>
        <h3>
          <i class="el-icon-connection"></i>
          连线属性配置
        </h3>

        <div class="dialog-body">
          <div class="properties-form">
            <div class="form-section">
              <h4>数据传输配置</h4>
              <div class="form-group">
                <label>数据集大小(MB)：</label>
                <input
                  type="number"
                  v-model.number="editingEdgeProperties.nMDataset"
                  :min="0"
                  placeholder="请输入数据集大小"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="dialog-actions">
          <button @click="saveEdgeProperties">
            <i class="el-icon-check"></i>
            保存
          </button>
          <button @click="closeEdgePropertiesDialog">
            <i class="el-icon-close"></i>
            取消
          </button>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { Graph, Shape } from '@antv/x6'
import { Stencil } from '@antv/x6-plugin-stencil'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { History } from '@antv/x6-plugin-history'

export default {
  name: 'TaskFlowEditor',
  props: {
    value: {
      type: Object,
      default: () => ({
        tasks: [],
        nodes: [],
        connections: []
      })
    },
    computingCenters: {
      type: Array,
      default: () => []
    },
    initialCommTypes: {
      type: Array,
      default: () => []
    },
    initialTasks: {
      type: Array,
      default: () => []
    },
    // 新增：用于恢复拓扑图的props
    nodeJsonData: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      graph: null,
      unifiedStencil: null,
      unifiedStencilId: 'unified-stencil-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11),

      // 任务数据
      tasks: [],
      selectedTaskId: '',

      // 任务管理对话框
      showTaskManagement: false,

      // 通信类型列表管理（保持单一默认类型）
      commTypeList: [],

      // 内部状态
      emitChangeTimer: null, // 防抖定时器

      // 颜色配置 - 清晰通透的色调
      colorPalette: [
        'rgba(16, 139, 233, 0.9)', // 蓝色
        'rgba(98, 179, 9, 0.9)', // 绿色
        'rgba(245, 166, 35, 0.9)', // 橙色
        'rgba(208, 2, 27, 0.9)', // 红色
        'rgba(144, 19, 254, 0.9)', // 紫色
        'rgba(80, 227, 194, 0.9)', // 青色
        'rgba(189, 16, 224, 0.9)', // 品红
        'rgba(34, 197, 94, 0.9)', // 翠绿
        'rgba(248, 113, 113, 0.9)', // 亮红
        'rgba(139, 87, 42, 0.9)' // 棕色
      ],

      // 节点属性编辑
      showNodePropertiesDialog: false,
      currentEditingNode: null,

      // 边属性编辑
      showEdgePropertiesDialog: false,
      currentEditingEdge: null,
      editingEdgeProperties: {},

      // 标记是否正在恢复状态
      isRestoring: false,
      editingProperties: {},

      // 任务列表（数组）
      taskList: [],

      // 任务表单管理
      showAddTaskForm: false,
      editingTaskIndex: -1,

      taskRules: {
        'c2netJobSpecification.mandatoryComputingCenterName': [
          { required: true, message: '请选择计算中心', trigger: 'change' }
        ],
        'c2netJobSpecification.nNodes': [
          { required: true, message: '请输入节点数量', trigger: 'blur' },
          { type: 'number', min: 1, max: 1000, message: '节点数量必须在1-1000之间', trigger: 'blur' }
        ],
        'c2netJobSpecification.nodeSpecification.nGraphicsCards': [
          { required: true, message: '请输入加速卡数量', trigger: 'blur' },
          { type: 'number', min: 1, max: 8, message: '加速卡数量必须在1-8之间', trigger: 'blur' }
        ]
      },
      taskForm: {
        id: '',
        name: '',
        c2netJobSpecification: {
          mandatoryComputingCenterName: '',
          nNodes: 4,
          nodeSpecification: {
            nGBMemoriesPerGraphicsCard: 32,
            nCpus: 8,
            nGBMemories: 100,
            nGraphicsCards: 4,
            graphicsCardType: 'ASCEND910'
          },
          datasetSpecification: {
            nMDataset: 10,
            srcCenterId: -1
          }
        }
      }
    }
  },

  mounted () {
    this.initTasks()
    this.$nextTick(() => {
      setTimeout(() => {
        this.initGraph()
        // 如果有nodeJsonData，立即恢复画布状态
        if (this.nodeJsonData) {
          // 减少延时，立即恢复
          this.$nextTick(() => {
            this.restoreFromNodeJson(this.nodeJsonData)
          })
        }
      }, 100) // 减少初始延时
    })
  },
  watch: {
    // 监听初始通信类型数据（仅在没有nodeJsonData时使用）
    initialCommTypes: {
      handler (newVal) {
        if (newVal && newVal.length > 0 && !this.nodeJsonData) {
          this.commTypeList = [...newVal]
          // 刷新stencil面板
          this.$nextTick(() => {
            this.refreshStencil()
          })
        }
      },
      immediate: true,
      deep: true
    },
    // 监听初始任务数据（仅在没有nodeJsonData时使用）
    initialTasks: {
      handler (newVal) {
        if (newVal && newVal.length > 0 && !this.nodeJsonData) {
          this.taskList = [...newVal]
          // 同步到 tasks 数组用于节点选择
          this.tasks = newVal.map((task, index) => ({
            id: task.id,
            name: task.name,
            color: this.getTaskColor(index),
            nodes: []
          }))
          // 刷新stencil面板
          this.$nextTick(() => {
            this.refreshStencil()
          })
        }
      },
      immediate: true,
      deep: true
    },
    // 监听nodeJsonData变化（仅用于拓扑图恢复）
    nodeJsonData: {
      handler (newVal) {
        if (newVal && this.graph) {
          // 立即恢复，不延迟
          this.$nextTick(() => {
            this.restoreFromNodeJson(newVal)
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  beforeDestroy () {
    // 清理定时器
    if (this.emitChangeTimer) {
      clearTimeout(this.emitChangeTimer)
      this.emitChangeTimer = null
    }

    // 销毁图形实例
    if (this.graph) {
      this.graph.dispose()
      this.graph = null
    }

    // 清理stencil
    if (this.unifiedStencil) {
      this.unifiedStencil = null
    }
  },

  computed: {
    // 计算待选区高度类
    stencilHeightClass () {
      const totalNodes = this.taskList.length + 1 // 任务节点 + 1个通信节点
      return totalNodes <= 8 ? 'height-small' : 'height-large'
    },

    // 获取可用的前置任务列表
    availablePrerequisiteTasks () {
      if (!this.taskList) return []

      return this.taskList.map((task, index) => ({
        id: task.id,
        label: `J${index}`, // 显示为J0, J1, J2等
        index: index
      }))
    }
  },

  methods: {
    // ========== 组件初始化和生命周期 ==========

    // 初始化任务
    initTasks () {
      if (this.value.tasks && this.value.tasks.length > 0) {
        this.tasks = [...this.value.tasks]
        this.selectedTaskId = this.tasks[0].id
      } else {
        this.tasks = []
        this.selectedTaskId = ''
      }

      // 初始化通信类型列表
      this.initCommTypes()
    },

    // 初始化通信类型列表
    initCommTypes () {
      // 默认创建allreduce通信类型
      this.commTypeList = [
        {
          id: 'comm_' + Date.now(),
          name: 'allreduce',
          algorithm: 'ring'
        }
      ]
    },

    // ========== 任务管理对话框 ==========

    // 打开任务管理对话框
    openTaskManagement () {
      this.showTaskManagement = true
    },

    // 关闭任务管理对话框
    closeTaskManagement () {
      this.showTaskManagement = false
      // 关闭时刷新stencil以显示最新的任务和通信类型
      this.refreshStencil()
    },

    // 刷新统一stencil面板
    refreshStencil () {
      if (!this.unifiedStencil) return

      // 清空现有stencil内容
      this.unifiedStencil.load([], 'unified')

      // 收集所有节点（计算任务 + 通信类型）
      const allNodes = []

      // 生成计算任务节点
      this.taskList.forEach((task, index) => {
        const node = this.graph.createNode({
          shape: 'compute-rect',
          label: `J${index}`,
          data: {
            boundTaskId: task.id,
            boundTaskIndex: index,
            taskName: task.name
          }
        })
        // 应用任务颜色
        const color = this.getTaskColor(index)
        this.applyTaskColor(node, color)
        allNodes.push(node)
      })

      // 生成通信类型节点（只显示一个默认通信类型）
      if (this.commTypeList.length > 0) {
        const commType = this.commTypeList[0] // 使用第一个（也是唯一的）通信类型
        const node = this.graph.createNode({
          shape: 'comm-rect',
          label: 'C',
          data: {
            boundCommTypeId: commType.id,
            boundCommTypeIndex: 0,
            commTypeName: commType.name,
            algorithm: commType.algorithm
          }
        })
        allNodes.push(node)
      }

      // 将所有节点加载到统一的stencil中
      if (allNodes.length > 0) {
        this.unifiedStencil.load(allNodes, 'unified')
      }

      // 检查是否需要滚动条
      this.$nextTick(() => {
        this.checkScrollable()
      })
    },

    // 检查stencil内容是否需要滚动
    checkScrollable () {
      const stencilContent = document.querySelector('.x6-widget-stencil-content')
      if (stencilContent) {
        const isScrollable = stencilContent.scrollHeight > stencilContent.clientHeight
        if (isScrollable) {
          stencilContent.classList.remove('no-scroll')
        } else {
          stencilContent.classList.add('no-scroll')
        }
      }
    },

    // ========== 任务管理 ==========

    // 根据任务索引获取颜色
    getTaskColor (taskIndex) {
      return this.colorPalette[taskIndex % this.colorPalette.length]
    },

    // 获取任务算子数量（画布上的算力节点数量）
    getTaskNodeCount (taskId) {
      const task = this.tasks.find(t => t.id === taskId)
      if (!task) return 0

      // 只统计算力中心节点，不包含通信节点
      return task.nodes.filter(nodeId => {
        const node = this.graph ? this.graph.getCellById(nodeId) : null
        return node && node.shape === 'compute-rect'
      }).length
    },

    // 获取任务配置中的节点数量（nNodes）
    getTaskNNodes (taskId) {
      const taskConfig = this.taskList.find(t => t.id === taskId)
      if (taskConfig && taskConfig.c2netJobSpecification) {
        return taskConfig.c2netJobSpecification.nNodes || 0
      }
      return 0
    },

    // 获取任务计算中心信息
    getTaskCenterInfo (taskId) {
      const taskConfig = this.taskList.find(t => t.id === taskId)
      if (taskConfig && taskConfig.c2netJobSpecification && taskConfig.c2netJobSpecification.mandatoryComputingCenterName) {
        return taskConfig.c2netJobSpecification.mandatoryComputingCenterName
      }
      return null
    },

    // 获取任务规格信息
    getTaskSpecs (taskId) {
      const taskConfig = this.taskList.find(t => t.id === taskId)
      if (taskConfig && taskConfig.c2netJobSpecification && taskConfig.c2netJobSpecification.nodeSpecification) {
        const specs = taskConfig.c2netJobSpecification.nodeSpecification
        return {
          graphicsCardType: specs.graphicsCardType || 'ASCEND910',
          nGraphicsCards: specs.nGraphicsCards || 4,
          nGBMemories: specs.nGBMemories || 100,
          nCpus: specs.nCpus || 8
        }
      }
      return null
    },

    // 获取任务完整配置
    getTaskConfig (taskId) {
      return this.taskList.find(t => t.id === taskId)
    },

    // 根据任务ID获取在taskList中的索引
    getTaskIndexById (taskId) {
      return this.taskList.findIndex(t => t.id === taskId)
    },

    // 自动生成任务名称
    generateTaskName () {
      const index = this.taskList.length
      return `计算中心${index}`
    },

    // 添加新任务
    addNewTask () {
      this.editingTaskIndex = -1
      this.resetTaskForm()
      this.showAddTaskForm = true
    },

    // 编辑任务
    editTask (task, index) {
      this.editingTaskIndex = index
      this.taskForm = JSON.parse(JSON.stringify(task))
      this.showAddTaskForm = true
    },

    // 删除任务
    deleteTask (index) {
      this.$confirm('确定要删除这个任务配置吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const taskToDelete = this.taskList[index]

        // 从taskList中删除
        this.taskList.splice(index, 1)

        // 从tasks数组中同步删除
        const taskIndex = this.tasks.findIndex(t => t.id === taskToDelete.id)
        if (taskIndex !== -1) {
          this.tasks.splice(taskIndex, 1)
        }

        // 重新编号所有任务名称和相关节点
        this.renumberAllTasks()

        this.$message.success('任务配置已删除')
        // 刷新stencil
        this.refreshStencil()
      }).catch(() => {
        // 取消删除
      })
    },

    // 保存任务
    saveTask () {
      this.$refs.taskForm.validate((valid) => {
        if (valid) {
          if (this.editingTaskIndex === -1) {
            // 添加新任务时自动生成名称和ID
            this.taskForm.name = this.generateTaskName()
            this.taskForm.id = 'task_' + Date.now()
            this.taskList.push(JSON.parse(JSON.stringify(this.taskForm)))

            // 同步到tasks数组用于节点选择
            const taskIndex = this.tasks.length
            this.tasks.push({
              id: this.taskForm.id,
              name: this.taskForm.name,
              color: this.getTaskColor(taskIndex),
              nodes: []
            })

            this.$message.success('任务配置添加成功')
          } else {
            // 编辑任务时更新任务配置，保持原有名称
            this.taskList[this.editingTaskIndex] = JSON.parse(JSON.stringify(this.taskForm))

            // 同步更新tasks数组
            const taskIndex = this.tasks.findIndex(t => t.id === this.taskForm.id)
            if (taskIndex !== -1) {
              this.tasks[taskIndex].name = this.taskForm.name
            }

            this.$message.success('任务配置保存成功')
          }

          this.closeTaskForm()
          // 刷新stencil
          this.refreshStencil()
        } else {
          this.$message.warning('请检查并完善表单信息')
          return false
        }
      })
    },

    // 关闭任务表单
    closeTaskForm () {
      this.showAddTaskForm = false
      this.editingTaskIndex = -1
      this.resetTaskForm()
    },

    // 重置任务表单
    resetTaskForm () {
      this.taskForm = {
        id: '',
        name: '',
        c2netJobSpecification: {
          mandatoryComputingCenterName: '',
          nNodes: 4,
          nodeSpecification: {
            nGBMemoriesPerGraphicsCard: 32,
            nCpus: 8,
            nGBMemories: 100,
            nGraphicsCards: 4,
            graphicsCardType: 'ASCEND910'
          },
          datasetSpecification: {
            nMDataset: 10,
            srcCenterId: -1
          }
        }
      }
    },



    // ========== 节点管理 ==========

    // 生成节点名称
    generateNodeName (taskId) {
      const taskIndex = this.tasks.findIndex(t => t.id === taskId)
      const task = this.tasks.find(t => t.id === taskId)

      if (!task) return `J${taskIndex}_0`

      // 使用任务中已记录的节点数量，确保命名一致
      const nodeCount = task.nodes.length

      return `J${taskIndex}_${nodeCount}`
    },

    // 重新编号任务节点
    renumberTaskNodes (taskId) {
      const task = this.tasks.find(t => t.id === taskId)
      if (!task) return

      const taskIndex = this.getTaskIndexById(taskId)
      if (taskIndex === -1) return

      // 获取该任务的所有节点
      const taskNodes = this.graph.getNodes().filter(node => {
        return node.shape === 'compute-rect' && task.nodes.includes(node.id)
      })

      // 按照节点在画布上的位置排序（从左到右，从上到下）
      taskNodes.sort((a, b) => {
        const posA = a.getPosition()
        const posB = b.getPosition()
        if (Math.abs(posA.y - posB.y) < 10) { // 同一行
          return posA.x - posB.x
        }
        return posA.y - posB.y
      })

      // 重新编号
      taskNodes.forEach((node, index) => {
        const newLabel = `J${taskIndex}_${index}`
        node.setLabel(newLabel)
      })
    },

    // 重新编号通信节点（使用全局序号）
    renumberCommNodes () {
      // 获取所有通信节点
      const commNodes = this.graph.getNodes().filter(node => {
        return node.shape === 'comm-rect'
      })

      // 按照节点在画布上的位置排序（从左到右，从上到下）
      commNodes.sort((a, b) => {
        const posA = a.getPosition()
        const posB = b.getPosition()
        if (Math.abs(posA.y - posB.y) < 10) { // 同一行
          return posA.x - posB.x
        }
        return posA.y - posB.y
      })

      // 重新编号（使用全局序号 C0, C1, C2...）
      commNodes.forEach((node, index) => {
        const newLabel = `C${index}`
        node.setLabel(newLabel)

        // 更新节点数据中的idInGroup
        const nodeData = node.getData()
        if (nodeData) {
          nodeData.idInGroup = index
          node.setData(nodeData)
        }
      })
    },

    // 重新编号所有任务
    renumberAllTasks () {
      // 重新生成任务名称
      this.taskList.forEach((task, index) => {
        const newName = `计算中心${index}`
        task.name = newName

        // 同步更新tasks数组中的名称
        const taskInTasks = this.tasks.find(t => t.id === task.id)
        if (taskInTasks) {
          taskInTasks.name = newName
        }
      })

      // 重新编号所有任务节点
      this.tasks.forEach((task, taskIndex) => {
        // 获取该任务的所有节点
        const taskNodes = this.graph.getNodes().filter(node => {
          return node.shape === 'compute-rect' && task.nodes.includes(node.id)
        })

        // 按照节点在画布上的位置排序（从左到右，从上到下）
        taskNodes.sort((a, b) => {
          const posA = a.getPosition()
          const posB = b.getPosition()
          if (Math.abs(posA.y - posB.y) < 10) { // 同一行
            return posA.x - posB.x
          }
          return posA.y - posB.y
        })

        // 重新编号节点标签
        taskNodes.forEach((node, nodeIndex) => {
          const newLabel = `J${taskIndex}_${nodeIndex}`
          node.setLabel(newLabel)
        })
      })

      // 触发数据变更事件
      this.emitChange()
    },

    // 处理计算中心选择框打开事件
    handleComputingCenterSelectOpen (visible) {
      if (visible && this.computingCenters.length === 0) {
        this.$message.warning('请先在计算中心列表中添加计算中心')
      }
    },

    // 处理计算中心选择变化
    handleComputingCenterChange (value) {
      if (!value) return

      // 查找选中的计算中心
      const selectedCenter = this.computingCenters.find(center => center.name === value)

      // 自动填充选中计算中心的属性（除了节点数量和加速卡数）
      if (selectedCenter) {
        // 保留用户设置的节点数量和加速卡数
        const userNNodes = this.taskForm.c2netJobSpecification.nNodes
        const userNGraphicsCards = this.taskForm.c2netJobSpecification.nodeSpecification.nGraphicsCards

        // 从计算中心属性自动填充其他配置
        this.taskForm.c2netJobSpecification.nodeSpecification = {
          ...this.taskForm.c2netJobSpecification.nodeSpecification,
          graphicsCardType: selectedCenter.graphicsCardType,
          nGBMemoriesPerGraphicsCard: selectedCenter.nGBMemoriesPerGraphicsCard,
          nCpus: selectedCenter.nCpus,
          nGBMemories: selectedCenter.nGBMemories,
          nGraphicsCards: userNGraphicsCards // 保留用户设置的加速卡数
        }

        // 保留用户设置的节点数量
        this.taskForm.c2netJobSpecification.nNodes = userNNodes
      }
    },

    // 应用任务颜色到节点
    applyTaskColor (node, color) {
      if (node.shape === 'compute-rect') {
        node.setAttrs({
          body: { fill: color, stroke: this.darkenColor(color, 20) },
          text: { fill: '#ffffff', fontWeight: 'bold', fontSize: 12 }
        })
      } else if (node.shape === 'comm-rect') {
        node.setAttrs({
          body: { stroke: this.darkenColor(color, 30), strokeWidth: 3 },
          text: { fill: '#ffffff', fontWeight: 'bold', fontSize: 12 }
        })
      }
    },

    // 加深颜色
    darkenColor (color, percent) {
      // 处理rgba格式的颜色
      if (color.startsWith('rgba')) {
        const rgba = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/)
        if (rgba) {
          const r = parseInt(rgba[1])
          const g = parseInt(rgba[2])
          const b = parseInt(rgba[3])
          const a = parseFloat(rgba[4])

          const factor = (100 - percent) / 100
          const newR = Math.round(r * factor)
          const newG = Math.round(g * factor)
          const newB = Math.round(b * factor)

          return `rgba(${newR}, ${newG}, ${newB}, ${a})`
        }
      }

      // 处理hex格式的颜色
      const hex = color.replace('#', '')
      const r = parseInt(hex.substr(0, 2), 16)
      const g = parseInt(hex.substr(2, 2), 16)
      const b = parseInt(hex.substr(4, 2), 16)

      const factor = (100 - percent) / 100
      const newR = Math.round(r * factor)
      const newG = Math.round(g * factor)
      const newB = Math.round(b * factor)

      return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`
    },

    // 渲染所有节点
    renderAllNodes () {
      this.graph.clearCells()

      if (this.value.nodes) {
        this.value.nodes.forEach(nodeData => {
          const node = this.graph.createNode({
            id: nodeData.id,
            shape: nodeData.type === 'comm' ? 'comm-rect' : 'compute-rect',
            label: nodeData.label,
            x: nodeData.x,
            y: nodeData.y
          })

          const nodeTask = this.tasks.find(task => task.nodes.includes(nodeData.id))
          if (nodeTask) {
            this.applyTaskColor(node, nodeTask.color)
          }

          this.graph.addNode(node)
        })

        if (this.value.connections) {
          this.value.connections.forEach(connData => {
            const sourceNode = this.graph.getCellById(connData.source)
            const targetNode = this.graph.getCellById(connData.target)
            if (sourceNode && targetNode) {
              const edge = this.graph.addEdge({
                id: connData.id,
                source: connData.source,
                target: connData.target
              })

              // 恢复边的属性数据
              if (connData.properties) {
                edge.setData(connData.properties)
              }
            }
          })
        }
      }
    },

    // ========== Graph 图形编辑器初始化和配置 ==========

    // 初始化图形编辑器
    initGraph () {
      const container = this.$refs.graphContainer
      if (!container) {
        console.error('容器不存在')
        return
      }

      try {
        // 完全按照sample配置Graph
        this.graph = new Graph({
          container: container,
          grid: true,
          mousewheel: {
            enabled: true,
            zoomAtMousePosition: true,
            modifiers: 'ctrl',
            minScale: 0.5,
            maxScale: 3
          },
          // 添加画布拖动功能
          panning: {
            enabled: true,
            modifiers: 'shift'
          },
          connecting: {
            router: 'manhattan',
            connector: {
              name: 'rounded',
              args: {
                radius: 8
              }
            },
            anchor: 'nodeCenter',
            connectionPoint: 'boundary',
            allowBlank: false,
            snap: {
              radius: 30
            },
            allowMulti: true,
            allowLoop: false,
            highlight: true,
            createEdge () {
              return new Shape.Edge({
                attrs: {
                  line: {
                    stroke: '#A2B1C3',
                    strokeWidth: 2,
                    targetMarker: {
                      name: 'block',
                      width: 12,
                      height: 8
                    }
                  },
                  // 添加不可见的宽线条用于点击检测
                  wrap: {
                    connection: true,
                    stroke: 'transparent',
                    strokeWidth: 20, // 增大点击区域到20px
                    fill: 'none'
                  }
                },
                zIndex: 0
              })
            },
            validateConnection ({ targetMagnet, sourceCell, targetCell }) {
              // 不允许连接到自己
              if (sourceCell === targetCell) {
                return false
              }
              // 必须有目标连接桩
              return !!targetMagnet
            }
          },
          highlighting: {
            magnetAdsorbed: {
              name: 'stroke',
              args: {
                attrs: {
                  fill: '#32c5ff',
                  stroke: '#32c5ff',
                  strokeWidth: 3,
                  r: 8
                }
              }
            },
            magnetAvailable: {
              name: 'stroke',
              args: {
                attrs: {
                  fill: 'rgba(50, 197, 255, 0.3)',
                  stroke: '#32c5ff',
                  strokeWidth: 2,
                  r: 6
                }
              }
            }
          },
          // 添加选中高亮效果
          selecting: {
            enabled: true,
            multiple: true,
            rubberband: true,
            movable: true,
            showNodeSelectionBox: true,
            showEdgeSelectionBox: false,
            pointerEvents: 'none'
          }
        })

        // 使用所有插件
        this.graph
          .use(new Transform({
            resizing: true,
            rotating: true
          }))
          .use(new Selection({
            rubberband: true,
            showNodeSelectionBox: true
          }))
          .use(new Keyboard())
          .use(new History())

        // 初始化stencil
        this.initStencil()

        // 注册自定义节点
        this.registerNodes()

        // 绑定快捷键和事件
        this.bindShortcuts()
        this.bindEvents()

        if (this.value.nodes && this.value.nodes.length > 0) {
          this.renderAllNodes()
        }
      } catch (error) {
        console.error('Graph初始化失败:', error)
      }
    },

    // 初始化统一节点面板
    initStencil () {
      // 创建统一的stencil - 支持多行显示
      this.unifiedStencil = new Stencil({
        target: this.graph,
        stencilGraphWidth: '100%',
        stencilGraphHeight: 'auto',
        collapsable: false,
        groups: [
          {
            name: 'unified',
            collapsed: false
          }
        ],
        layoutOptions: {
          columns: 8,
          columnWidth: 90,
          rowHeight: 35,
          marginX: 10,
          marginY: 1,
          resizeToFit: true
        }
      })

      // 挂载到统一容器
      document.getElementById(this.unifiedStencilId).appendChild(this.unifiedStencil.container)
    },

    // 注册自定义节点类型
    registerNodes () {
      // 定义连接桩
      const ports = {
        groups: {
          top: {
            position: 'top',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden'
                }
              }
            }
          },
          right: {
            position: 'right',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden'
                }
              }
            }
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden'
                }
              }
            }
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden'
                }
              }
            }
          }
        },
        items: [
          { group: 'top' },
          { group: 'right' },
          { group: 'bottom' },
          { group: 'left' }
        ]
      }

      // 注册算力中心节点
      Graph.registerNode(
        'compute-rect',
        {
          inherit: 'rect',
          width: 80,
          height: 35,
          attrs: {
            body: {
              strokeWidth: 2,
              stroke: '#32c5ff',
              fill: '#e8f4ff',
              rx: 6,
              ry: 6
            },
            text: {
              fontSize: 12,
              fill: '#068817',
              fontWeight: 'bold'
            }
          },
          ports: { ...ports }
        },
        true
      )

      // 注册通信节点
      Graph.registerNode(
        'comm-rect',
        {
          inherit: 'rect',
          width: 80,
          height: 35,
          attrs: {
            body: {
              strokeWidth: 2,
              stroke: '#1890ff',
              fill: '#f0f8ff',
              rx: 6,
              ry: 6
            },
            text: {
              fontSize: 12,
              fill: '#0b77e1',
              fontWeight: 'bold'
            }
          },
          ports: { ...ports }
        },
        true
      )

      // 初始加载stencil内容
      this.refreshStencil()
    },

    // ========== 事件绑定和交互 ==========

    // 绑定快捷键
    bindShortcuts () {
      // 复制粘贴快捷键
      this.graph.bindKey(['meta+c', 'ctrl+c'], () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          this.graph.copy(cells)
        }
        return false
      })

      this.graph.bindKey(['meta+x', 'ctrl+x'], () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          this.graph.cut(cells)
        }
        return false
      })

      this.graph.bindKey(['meta+v', 'ctrl+v'], () => {
        if (!this.graph.isClipboardEmpty()) {
          const cells = this.graph.paste({ offset: 32 })
          this.graph.cleanSelection()
          this.graph.select(cells)
        }
        return false
      })

      // 撤销重做快捷键
      this.graph.bindKey(['meta+z', 'ctrl+z'], () => {
        if (this.graph.canUndo()) {
          this.graph.undo()
        }
        return false
      })

      this.graph.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
        if (this.graph.canRedo()) {
          this.graph.redo()
        }
        return false
      })

      // 全选快捷键
      this.graph.bindKey(['meta+a', 'ctrl+a'], () => {
        const nodes = this.graph.getNodes()
        if (nodes) {
          this.graph.select(nodes)
        }
      })

      // 删除快捷键
      this.graph.bindKey('backspace', () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          // 区分节点和连线的删除
          const edges = cells.filter(cell => cell.isEdge())
          const nodes = cells.filter(cell => cell.isNode())

          if (edges.length > 0) {
            edges.forEach(edge => {
              this.graph.removeCell(edge)
            })
            if (edges.length === 1) {
              this.$message.success('连线已删除')
            } else {
              this.$message.success(`已删除${edges.length}条连线`)
            }
          }

          if (nodes.length > 0) {
            // 记录被删除的任务节点，用于重新编号
            const deletedTaskNodes = new Set()
            const deletedCommTypeNodes = new Set()

            nodes.forEach(node => {
              // 只有算力中心节点才需要从任务中移除
              if (node.shape === 'compute-rect') {
                // 从任务中移除节点
                this.tasks.forEach(task => {
                  const index = task.nodes.indexOf(node.id)
                  if (index > -1) {
                    task.nodes.splice(index, 1)
                    deletedTaskNodes.add(task.id)
                  }
                })
              } else if (node.shape === 'comm-rect') {
                // 记录被删除的通信节点类型
                const nodeData = node.getData()
                if (nodeData && nodeData.boundCommTypeId) {
                  deletedCommTypeNodes.add(nodeData.boundCommTypeId)
                }
              }
              this.graph.removeCell(node)
            })

            // 重新编号受影响的任务节点
            deletedTaskNodes.forEach(taskId => {
              this.renumberTaskNodes(taskId)
            })

            // 重新编号所有通信节点
            if (deletedCommTypeNodes.size > 0) {
              this.renumberCommNodes()
            }

            if (nodes.length === 1) {
              this.$message.success('节点已删除')
            } else {
              this.$message.success(`已删除${nodes.length}个节点`)
            }
          }
        }
      })

      // 缩放快捷键
      this.graph.bindKey(['ctrl+1', 'meta+1'], () => {
        const zoom = this.graph.zoom()
        if (zoom < 1.5) {
          this.graph.zoom(0.1)
        }
      })

      this.graph.bindKey(['ctrl+2', 'meta+2'], () => {
        const zoom = this.graph.zoom()
        if (zoom > 0.5) {
          this.graph.zoom(-0.1)
        }
      })

      // 节点双击编辑
      this.graph.on('node:dblclick', ({ node, e }) => {
        // 阻止默认的文字选中行为
        e.preventDefault()
        this.openNodePropertiesDialog(node)
      })

      // 边双击编辑
      this.graph.on('edge:dblclick', ({ edge, e }) => {
        // 阻止默认的文字选中行为
        e.preventDefault()
        this.openEdgePropertiesDialog(edge)
      })

      // 连线交互事件
      this.graph.on('edge:click', ({ edge }) => {
        this.graph.select(edge)
      })

      this.graph.on('edge:mouseenter', ({ edge }) => {
        // 增强悬停效果，提示可以双击
        edge.setAttrs({
          line: {
            stroke: '#32c5ff',
            strokeWidth: 4 // 增加线宽
          }
        })
        // 改变鼠标样式
        document.body.style.cursor = 'pointer'
      })

      this.graph.on('edge:mouseleave', ({ edge }) => {
        const isSelected = this.graph.isSelected(edge)
        if (!isSelected) {
          edge.setAttrs({
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2
            }
          })
        }
        // 恢复鼠标样式
        document.body.style.cursor = 'default'
      })

      // 选中状态管理
      this.graph.on('cell:selected', ({ cell }) => {
        if (cell.isEdge()) {
          cell.setAttrs({
            line: {
              stroke: '#1890ff',
              strokeWidth: 4
            }
          })
        }
      })

      this.graph.on('cell:unselected', ({ cell }) => {
        if (cell.isEdge()) {
          cell.setAttrs({
            line: {
              stroke: '#A2B1C3',
              strokeWidth: 2
            }
          })
        }
      })
    },

    // 绑定图形编辑器事件
    bindEvents () {
      // 控制连接桩显示/隐藏
      const showPorts = (ports, show) => {
        for (let i = 0, len = ports.length; i < len; i += 1) {
          ports[i].style.visibility = show ? 'visible' : 'hidden'
        }
      }

      // 节点悬停时显示连接桩
      this.graph.on('node:mouseenter', () => {
        const container = this.$refs.graphContainer
        const ports = container.querySelectorAll('.x6-port-body')
        showPorts(ports, true)
      })

      this.graph.on('node:mouseleave', () => {
        const container = this.$refs.graphContainer
        const ports = container.querySelectorAll('.x6-port-body')
        showPorts(ports, false)
      })

      // 连线过程中的事件监听
      this.graph.on('edge:connecting', ({ isNew }) => {
        if (isNew) {
          // 显示所有连接桩以便连线
          const container = this.$refs.graphContainer
          const ports = container.querySelectorAll('.x6-port-body')
          showPorts(ports, true)
        }
      })

      this.graph.on('edge:connected', ({ isNew }) => {
        if (isNew) {
          // 连线完成后隐藏连接桩
          setTimeout(() => {
            const container = this.$refs.graphContainer
            const ports = container.querySelectorAll('.x6-port-body')
            showPorts(ports, false)
          }, 500)
        }
      })

      // 连线被移除时的处理
      this.graph.on('edge:removed', () => {
        const container = this.$refs.graphContainer
        const ports = container.querySelectorAll('.x6-port-body')
        showPorts(ports, false)
      })

      // 节点生命周期事件
      this.graph.on('node:added', ({ node }) => {
        // 如果正在恢复状态，不触发创建逻辑
        if (this.isRestoring) {
          return
        }

        // 检查节点是否从stencil拖拽而来，并且包含绑定信息
        const nodeData = node.getData()
        if (!nodeData) {
          return
        }

        // 处理算力节点
        if (node.shape === 'compute-rect' && nodeData.boundTaskId) {
          const taskId = nodeData.boundTaskId
          const taskIndex = nodeData.boundTaskIndex

          // 生成唯一的节点名称
          const task = this.tasks.find(t => t.id === taskId)
          if (!task) {
            // 如果任务不存在，创建一个新的任务条目
            this.tasks.push({
              id: taskId,
              name: nodeData.taskName || `计算中心${taskIndex}`,
              color: this.getTaskColor(taskIndex),
              nodes: []
            })
          }

          // 生成节点名称：J{taskIndex}_{nodeCount}
          const targetTask = this.tasks.find(t => t.id === taskId)
          const nodeCount = targetTask.nodes.length
          const nodeName = `J${taskIndex}_${nodeCount}`

          // 设置节点标签和颜色
          node.setLabel(nodeName)
          const color = this.getTaskColor(taskIndex)
          this.applyTaskColor(node, color)

          // 将节点ID添加到任务的节点列表中
          targetTask.nodes.push(node.id)

          this.emitChange()
          this.$message.success(`算力节点 ${nodeName} 创建成功`)
        }

        // 处理通信节点
        else if (node.shape === 'comm-rect' && nodeData.boundCommTypeId) {
          // 获取所有通信节点进行全局编号
          const allCommNodes = this.graph.getNodes().filter(n => n.shape === 'comm-rect')
          const nodeCount = allCommNodes.length - 1 // 减去当前节点

          const nodeName = `C${nodeCount}`
          node.setLabel(nodeName)

          // 设置通信节点的详细属性
          const commNodeData = {
            name: nodeData.commTypeName || 'allreduce',
            algorithm: nodeData.algorithm || 'ring',
            idInGroup: nodeCount,
            nMDataset: 30,
            ...nodeData // 保留绑定信息
          }
          node.setData(commNodeData)

          this.emitChange()
          this.$message.success(`通信节点 ${nodeName} 创建成功`)
        }
      })

      this.graph.on('node:removed', ({ node }) => {
        if (this.pendingNodeData && this.pendingNodeData.nodeId === node.id) {
          return
        }

        if (node.shape === 'compute-rect') {
          this.tasks.forEach(task => {
            const index = task.nodes.indexOf(node.id)
            if (index > -1) {
              task.nodes.splice(index, 1)
            }
          })
        }

        this.emitChange()
      })

      // 连线和位置变化事件
      this.graph.on('edge:connected', () => this.emitChange())
      this.graph.on('edge:removed', () => this.emitChange())
      this.graph.on('node:change:position', () => this.emitChange())

      // 右键菜单事件
      this.graph.on('node:contextmenu', ({ e, node }) => {
        this.showNodeContextMenu(e, node)
      })

      this.graph.on('edge:contextmenu', ({ e, edge }) => {
        this.showEdgeContextMenu(e, edge)
      })

      this.graph.on('blank:contextmenu', ({ e }) => {
        this.showCanvasContextMenu(e)
      })
    },

    // 显示节点右键菜单
    showNodeContextMenu (e, node) {
      e.preventDefault()
      if (confirm('是否删除此节点？')) {
        if (node.shape === 'compute-rect') {
          this.tasks.forEach(task => {
            const index = task.nodes.indexOf(node.id)
            if (index > -1) {
              task.nodes.splice(index, 1)
            }
          })
        }
        this.graph.removeCell(node)
        this.$message.success('节点已删除')
      }
    },

    // 显示连线右键菜单
    showEdgeContextMenu (e, edge) {
      e.preventDefault()
      if (confirm('是否删除此连线？')) {
        this.graph.removeCell(edge)
        this.$message.success('连线已删除')
      }
    },

    // 显示画布右键菜单
    showCanvasContextMenu (e) {
      e.preventDefault()
      this.graph.zoomToFit({ padding: 20 })
    },

    // ========== 数据变更和同步 ==========

    // 发送数据变更事件
    emitChange () {
      if (!this.graph) return

      // 使用防抖，避免频繁触发
      if (this.emitChangeTimer) {
        clearTimeout(this.emitChangeTimer)
      }

      this.emitChangeTimer = setTimeout(() => {
        const nodes = this.graph.getNodes().map(node => {
          let taskId = null

          // 只有算力中心节点才有任务关联
          if (node.shape === 'compute-rect') {
            for (const task of this.tasks) {
              if (task.nodes.includes(node.id)) {
                taskId = task.id
                break
              }
            }
          }

          return {
            id: node.id,
            label: node.getLabel(),
            x: node.getPosition().x,
            y: node.getPosition().y,
            type: node.shape === 'comm-rect' ? 'comm' : 'compute',
            taskId: taskId,
            properties: node.getData() || {} // 包含节点属性
          }
        })

        const connections = this.graph.getEdges().map(edge => ({
          id: edge.id,
          source: edge.getSourceCellId(),
          target: edge.getTargetCellId(),
          properties: edge.getData() || {} // 包含边属性
        }))

        // 生成c2netJobGroupList用于协同任务
        const c2netJobGroupList = this.generateC2NetJobGroupList()

        this.$emit('input', {
          tasks: this.tasks,
          nodes,
          connections,
          c2netJobGroupList // 添加任务组列表
        })
      }, 100) // 100ms防抖
    },

    // ========== 节点属性编辑 ==========

    // 打开节点属性编辑对话框
    openNodePropertiesDialog (node) {
      this.currentEditingNode = node

      // 加载已有的节点属性，如果没有则使用默认值
      const existingProps = node.getData() || {}

      if (node.shape === 'comm-rect') {
        // 计算当前画布上通信节点的数量，用于自动生成组内ID
        const commNodes = this.graph.getNodes().filter(n => n.shape === 'comm-rect')
        const autoIdInGroup = existingProps.idInGroup !== undefined ? existingProps.idInGroup : commNodes.length - 1

        this.editingProperties = {
          name: existingProps.name || 'allreduce',
          algorithm: existingProps.algorithm || 'ring',
          idInGroup: autoIdInGroup,
          nMDataset: existingProps.nMDataset ?? 30
        }
      } else {
        // 计算节点编辑运行时间和前置任务
        this.editingProperties = {
          runDurationTime: existingProps.runDurationTime ?? 200,
          prerequisiteTasks: existingProps.prerequisiteTasks || []
        }
      }

      this.showNodePropertiesDialog = true
    },

    // 关闭节点属性编辑对话框
    closeNodePropertiesDialog () {
      this.currentEditingNode = null
      this.showNodePropertiesDialog = false
    },

    // 保存节点属性
    saveNodeProperties () {
      if (!this.currentEditingNode) return

      const node = this.currentEditingNode

      // 保存节点属性到节点数据中
      node.setData(this.editingProperties)

      // 更新节点标签
      if (node.shape === 'comm-rect' && this.editingProperties.name) {
        // 通信节点显示通信类型名称
        node.setLabel(this.editingProperties.name)
      }
      // 计算节点不修改标签，保持原有名称

      // 关闭对话框
      this.closeNodePropertiesDialog()
      this.emitChange()
      this.$message.success('节点属性已保存')
    },

    // ========== 边属性编辑 ==========

    // 打开边属性编辑对话框
    openEdgePropertiesDialog (edge) {
      this.currentEditingEdge = edge

      // 加载已有的边属性，如果没有则使用默认值
      const existingProps = edge.getData() || {}

      this.editingEdgeProperties = {
        nMDataset: existingProps.nMDataset ?? 0
      }

      this.showEdgePropertiesDialog = true
    },

    // 关闭边属性编辑对话框
    closeEdgePropertiesDialog () {
      this.currentEditingEdge = null
      this.showEdgePropertiesDialog = false
    },

    // 保存边属性
    saveEdgeProperties () {
      if (!this.currentEditingEdge) return

      const edge = this.currentEditingEdge

      // 保存边属性到边数据中
      edge.setData(this.editingEdgeProperties)

      // 关闭对话框
      this.closeEdgePropertiesDialog()
      this.emitChange()
      this.$message.success('连线属性已保存')
    },

    // ========== 数据生成和转换 ==========

    // 为其他组件提供任务流数据（用于协同任务对话框等）
    prepareTaskFlowData () {
      // 验证任务列表
      if (!this.taskList || this.taskList.length === 0) {
        this.$message.warning('请先添加任务配置')
        return null
      }

      // 验证画布节点
      if (!this.graph || this.graph.getNodes().length === 0) {
        this.$message.warning('请先在画布上添加节点')
        return null
      }

      // 构建任务组列表
      const c2netJobGroupList = this.generateC2NetJobGroupList()
      if (!c2netJobGroupList || c2netJobGroupList.length === 0) {
        this.$message.warning('无法生成有效的任务组配置')
        return null
      }

      // 构建计算中心列表（从taskList中提取）
      const c2netComputingCenterList = this.generateComputingCenterList()

      // 构建配置对象
      const scheduleConfig = {
        scheduleType: '本地调度'
      }

      const c2netJobConfig = {
        useJsonFile: false,
        jsonFile: null,
        jsonFileList: [],
        c2netJobGroupList: c2netJobGroupList
      }

      // 收集节点数据
      const nodeJson = this.collectNodeData()

      // 返回完整的任务数据（不包含networkConfig，由外层组件提供）
      return {
        c2netComputingCenterList,
        c2netJobConfig,
        scheduleConfig,
        params: {
          nodeJson: nodeJson
        }
      }
    },

    // 收集当前画布的节点数据（节点画图数据、通信类型、任务配置）
    collectNodeData () {
      if (!this.graph) {
        return null
      }

      const nodes = this.graph.getNodes().map(node => {
        let taskId = null
        // 只有算力中心节点才有任务关联
        if (node.shape === 'compute-rect') {
          for (const task of this.tasks) {
            if (task.nodes.includes(node.id)) {
              taskId = task.id
              break
            }
          }
        }

        const nodeData = {
          id: node.id,
          x: node.getPosition().x,
          y: node.getPosition().y,
          shape: node.shape,
          label: node.getLabel(),
          data: node.getData() || {},
          taskId: taskId
        }
        return nodeData
      })

      const edges = this.graph.getEdges().map(edge => ({
        id: edge.id,
        source: edge.getSourceCellId(),
        target: edge.getTargetCellId(),
        data: edge.getData() || {} // 包含边属性
      }))

      const nodeJsonData = {
        nodes,
        edges,
        commTypeList: [...this.commTypeList],
        taskList: [...this.taskList],
        tasks: [...this.tasks]
      }

      return nodeJsonData
    },

    // 从nodeJson数据恢复拓扑图状态（仅恢复节点和连线）
    restoreFromNodeJson (nodeJson) {
      if (!nodeJson || !this.graph) {
        return
      }

      // 设置恢复状态标志
      this.isRestoring = true

      // 清空当前画布
      this.graph.clearCells()

      // 恢复通信类型列表
      if (nodeJson.commTypeList && nodeJson.commTypeList.length > 0) {
        this.commTypeList = [...nodeJson.commTypeList]
      }

      // 恢复任务配置列表
      if (nodeJson.taskList && nodeJson.taskList.length > 0) {
        this.taskList = [...nodeJson.taskList]
      }

      // 恢复任务数组
      if (nodeJson.tasks && nodeJson.tasks.length > 0) {
        this.tasks = [...nodeJson.tasks]
      }

      // 恢复节点（批量添加优化性能）
      if (nodeJson.nodes && nodeJson.nodes.length > 0) {
        const nodesToAdd = []

        nodeJson.nodes.forEach(nodeData => {
          const node = this.graph.createNode({
            id: nodeData.id,
            shape: nodeData.shape,
            label: nodeData.label,
            x: nodeData.x,
            y: nodeData.y
          })

          // 设置节点数据
          if (nodeData.data) {
            node.setData(nodeData.data)
          }

          // 应用任务颜色（仅对算力节点）
          if (nodeData.taskId && nodeData.shape === 'compute-rect') {
            const task = this.tasks.find(t => t.id === nodeData.taskId)
            if (task) {
              this.applyTaskColor(node, task.color)
            }
          }

          nodesToAdd.push(node)
        })

        // 批量添加节点，提高性能
        this.graph.addNodes(nodesToAdd)
      }

      // 恢复连线（批量添加优化性能）
      if (nodeJson.edges && nodeJson.edges.length > 0) {
        const edgesToAdd = []

        nodeJson.edges.forEach((edgeData, index) => {
          const sourceNode = this.graph.getCellById(edgeData.source)
          const targetNode = this.graph.getCellById(edgeData.target)
          if (sourceNode && targetNode) {
            const edge = this.graph.createEdge({
              id: edgeData.id,
              source: edgeData.source,
              target: edgeData.target,
              attrs: {
                line: {
                  stroke: '#A2B1C3',
                  strokeWidth: 2,
                  targetMarker: {
                    name: 'block',
                    width: 12,
                    height: 8
                  }
                }
              }
            })
            edgesToAdd.push(edge)
          } else {
            console.warn(`连线${index + 1}恢复失败，找不到节点:`, edgeData.source, '->', edgeData.target)
          }
        })

        // 批量添加连线，提高性能
        if (edgesToAdd.length > 0) {
          this.graph.addEdges(edgesToAdd)
        }
      }

      // 刷新stencil面板以显示恢复的任务和通信类型
      this.$nextTick(() => {
        this.refreshStencil()
      })

      // 延迟重置恢复状态标志，确保恢复过程完全完成
      setTimeout(() => {
        this.isRestoring = false
      }, 500)

      this.emitChange()
    },

    // 生成C2Net任务组列表
    generateC2NetJobGroupList () {
      const jobGroupList = []

      if (!this.taskList || this.taskList.length === 0) {
        return jobGroupList
      }

      // 收集通信算子规格
      const commNodes = this.graph.getNodes().filter(node => node.shape === 'comm-rect')
      const collectiveCommunicationList = []

      if (commNodes.length > 0) {
        const commSpecs = commNodes.map((node, index) => {
          const nodeData = node.getData() || {}
          return {
            idInGroup: index,
            nMDataset: nodeData.nMDataset || 30
          }
        })

        collectiveCommunicationList.push({
          collectiveCommunicationSpecification: {
            name: 'allreduce',
            algorithm: 'ring'
          },
          copiedCollectiveCommunicationSpecificationList: commSpecs
        })
      }

      // 构建所有任务的c2netJobList
      const c2netJobList = []

      this.taskList.forEach((task, taskIndex) => {
        // 获取该任务关联的计算节点
        const taskNodes = this.graph.getNodes().filter(node => {
          return node.shape === 'compute-rect' && this.tasks.some(t =>
            t.id === task.id && t.nodes.includes(node.id)
          )
        })

        if (taskNodes.length === 0) {
          return
        }

        // 构建计算操作符列表
        const computingOperatorList = []
        taskNodes.forEach((node, nodeIndex) => {
          const nodeData = node.getData() || {}
          const taskObj = this.tasks.find(t => t.id === task.id)
          const nodeIndexInTask = taskObj ? taskObj.nodes.indexOf(node.id) : nodeIndex

          computingOperatorList.push({
            idInJob: nodeIndexInTask >= 0 ? nodeIndexInTask : nodeIndex,
            runDurationTime: nodeData.runDurationTime || 200
          })
        })

        const copiedJobSpecs = [{
          jobIdInGroup: taskIndex,
          computingOperatorList: computingOperatorList
        }]

        c2netJobList.push({
          c2netJobSpecification: {
            ...task.c2netJobSpecification
          },
          copiedC2netJobSpecificationList: copiedJobSpecs
        })
      })

      // 创建任务组
      const jobGroup = {
        c2netJobGroupSpecification: {
          submissionTime: 1.0,
          subGraphList: [
            {
              subGraphIndex: 0,
              collaborativeSchedule: true,
              executeEpochs: 3,
              subGraphTopology: this.generateSubGraphTopology()
            }
          ],
          collectiveCommunicationList: collectiveCommunicationList,
          c2netJobList: c2netJobList
        },
        nCopiedC2netJobGroups: 1
      }

      jobGroupList.push(jobGroup)
      return jobGroupList
    },

    // 生成计算中心列表
    generateComputingCenterList () {
      const centerList = []
      const uniqueCenters = new Map()

      if (!this.taskList || this.taskList.length === 0) {
        return centerList
      }

      // 从taskList中提取唯一的计算中心配置
      this.taskList.forEach((task) => {
        if (!task.c2netJobSpecification) {
          return
        }

        const centerName = task.c2netJobSpecification.mandatoryComputingCenterName
        if (centerName && !uniqueCenters.has(centerName)) {
          const nodeSpec = task.c2netJobSpecification.nodeSpecification
          if (!nodeSpec) {
            return
          }

          const centerConfig = {
            c2netComputingCenterSpecification: {
              nodeSpecification: {
                nGBMemoriesPerGraphicsCard: nodeSpec.nGBMemoriesPerGraphicsCard || 32,
                nCpus: nodeSpec.nCpus || 8,
                nGBMemories: nodeSpec.nGBMemories || 100,
                nGraphicsCards: nodeSpec.nGraphicsCards || 4,
                graphicsCardType: nodeSpec.graphicsCardType || 'ASCEND910'
              },
              nNodes: task.c2netJobSpecification.nNodes || 4,
              pOPS: this.calculatePOPS(task.c2netJobSpecification),
              name: centerName
            },
            nCopiedC2netComputingCenters: 1
          }

          uniqueCenters.set(centerName, centerConfig)
          centerList.push(centerConfig)
        }
      })

      return centerList
    },

    // 计算pOPS值
    calculatePOPS (jobSpec) {
      const basePerformance = {
        ASCEND910: 320,
        A100: 312,
        V100: 125,
        'ENFLAME-T20': 200
      }

      const cardPerformance = basePerformance[jobSpec.nodeSpecification.graphicsCardType] || 100
      return jobSpec.nNodes * jobSpec.nodeSpecification.nGraphicsCards * cardPerformance
    },

    // 生成子图拓扑数据
    generateSubGraphTopology () {
      if (!this.graph) return []

      const topology = []
      const nodes = this.graph.getNodes()
      const edges = this.graph.getEdges()

      // 找到所有入口节点（没有传入边的节点）
      const nodeIds = nodes.map(node => node.id)
      const targetNodeIds = edges.map(edge => edge.getTargetCellId())
      const entryNodes = nodeIds.filter(nodeId => !targetNodeIds.includes(nodeId))

      // 为入口节点添加入口边
      entryNodes.forEach(nodeId => {
        const node = this.graph.getCellById(nodeId)
        if (node) {
          topology.push({
            directedEdge: `->${node.getLabel()}`,
            nMDataset: 0 // 入口边默认为0
          })
        }
      })

      // 添加所有连接边，使用边的属性数据
      edges.forEach(edge => {
        const sourceNode = this.graph.getCellById(edge.getSourceCellId())
        const targetNode = this.graph.getCellById(edge.getTargetCellId())

        if (sourceNode && targetNode) {
          // 获取边的属性数据
          const edgeData = edge.getData() || {}
          const nMDataset = edgeData.nMDataset || 0

          topology.push({
            directedEdge: `${sourceNode.getLabel()}->${targetNode.getLabel()}`,
            nMDataset: nMDataset
          })
        }
      })

      // 最后追加基于前置任务配置的有向边
      nodes.forEach(node => {
        if (node.shape === 'compute-rect') {
          const nodeData = node.getData() || {}
          const prerequisiteTasks = nodeData.prerequisiteTasks || []

          prerequisiteTasks.forEach(prerequisiteTaskId => {
            // 找到前置任务在taskList中的索引
            const prerequisiteTaskIndex = this.taskList.findIndex(t => t.id === prerequisiteTaskId)

            if (prerequisiteTaskIndex !== -1) {
              // 生成有向边：任务编号->当前节点标签
              topology.push({
                directedEdge: `${prerequisiteTaskIndex}->${node.getLabel()}`,
                nMDataset: 0 // 前置任务边默认为0
              })
            }
          })
        }
      })

      return topology
    },

    // ========== 工具方法和验证 ==========

    // 数字输入验证
    validateNumberInput (event) {
      const keyCode = event.keyCode
      // 允许的按键：数字键(48-57, 96-105)、退格键(8)、删除键(46)、Tab键(9)、箭头键(37-40)
      if (!(keyCode >= 48 && keyCode <= 57) &&
          !(keyCode >= 96 && keyCode <= 105) &&
          keyCode !== 8 && keyCode !== 46 &&
          keyCode !== 9 && !(keyCode >= 37 && keyCode <= 40)) {
        event.preventDefault()
      }
    },

    // 验证任务流配置
    validateTaskFlow () {
      if (!this.graph) return false
      const nodes = this.graph.getNodes()
      return nodes.length > 0
    }

  }
}
</script>

<style>
/* 完全按照sample的样式 */
.task-flow-editor {
  width: 100%;
  height: 600px;
}

.task-flow-editor .section-header {
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(18, 137, 221, 0.3);
  padding-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-flow-editor .section-title {
  color: #32c5ff;
  font-size: 16px;
  font-weight: bold;
  padding-left: 8px;
  border-left: 3px solid #32c5ff;
}

.task-flow-editor .section-content {
  height: calc(100% - 50px);
}

.task-flow-editor .flow-editor-container {
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(18, 137, 221, 0.3);
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(18, 137, 221, 0.1);
  position: relative;
}

.task-flow-editor .stencil-wrapper {
  width: 100%;
  position: relative;
  border-bottom: 1px solid rgba(18, 137, 221, 0.3);
  background: linear-gradient(135deg, rgba(18, 137, 221, 0.08) 0%, rgba(50, 197, 255, 0.05) 100%);
  overflow: hidden;
  transition: height 0.3s ease;
}

/* 动态高度类 */
.task-flow-editor .stencil-wrapper.height-small {
  height: 60px;
}

.task-flow-editor .stencil-wrapper.height-large {
  height: 90px;
}

.task-flow-editor .graph-container {
  width: 100%;
  background-color: rgba(18, 137, 221, 0.02);
  position: relative;
  flex: 1;
  overflow: visible;
}

/* 画布右下角浮动提示 */
.task-flow-editor .help-tips {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 130px;
  background: rgba(0, 20, 60, 0.03);
  border: none;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 9px;
  opacity: 0.4;
  backdrop-filter: blur(1px);
  z-index: 1;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.task-flow-editor .help-tips:hover {
  opacity: 0.8;
}

.task-flow-editor .graph-container:hover .help-tips {
  opacity: 0.6;
}

.task-flow-editor .tip-item {
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 2px;
  line-height: 1.2;
  font-size: 9px;
  white-space: nowrap;
  font-weight: 400;
}

.task-flow-editor .tip-item:last-child {
  margin-bottom: 0;
}

/* X6相关样式（分组布局专用） */
.x6-widget-stencil {
  background: transparent !important;
  border: none !important;
  height: 100% !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: visible !important;
}

.x6-widget-stencil-title {
  display: none;
}

.x6-widget-stencil-group-title {
  display: none;
}

/* 深层嵌套容器优化 */
.x6-widget-stencil-group {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: auto !important;
}

.x6-widget-stencil-group-content {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: auto !important;
}

.x6-widget-stencil-group .x6-graph {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* 优化 stencil 内的节点显示 */
.x6-widget-stencil-group .x6-graph {
  overflow: visible !important;
  height: auto !important;
  min-height: 100% !important;
}

.x6-widget-stencil-group .x6-graph .x6-graph-svg {
  overflow: visible !important;
  height: auto !important;
  min-height: 100% !important;
}

/* 隐藏 stencil 的背景和网格 */
.x6-widget-stencil-group .x6-graph .x6-graph-background,
.x6-widget-stencil-group .x6-graph .x6-graph-grid {
  display: none !important;
}

.x6-widget-stencil-content {
  height: 100% !important;
  width: 100% !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 0px 10px 5px 10px !important;
  margin: 0 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* 美观的细滚动条样式 */
.x6-widget-stencil-content::-webkit-scrollbar {
  width: 3px;
  height: 3px;
}

.x6-widget-stencil-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  margin: 0px;
}

.x6-widget-stencil-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(18, 137, 221, 0.7) 0%, rgba(50, 197, 255, 0.5) 100%);
  border-radius: 2px;
  border: none;
  box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.x6-widget-stencil-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(18, 137, 221, 1) 0%, rgba(50, 197, 255, 0.8) 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.x6-widget-stencil-content::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, rgba(14, 110, 177, 1) 0%, rgba(40, 158, 204, 0.9) 100%);
}

/* Firefox 滚动条样式 */
.x6-widget-stencil-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(18, 137, 221, 0.7) rgba(0, 0, 0, 0.05);
}

/* 当内容不需要滚动时隐藏滚动条 */
.x6-widget-stencil-content.no-scroll::-webkit-scrollbar {
  display: none;
}

.x6-widget-stencil-content.no-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

/* stencil的滚动条样式 - 美化版 */
.x6-widget-stencil-content::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

.x6-widget-stencil-content::-webkit-scrollbar-track {
  background: rgba(18, 137, 221, 0.05);
  border-radius: 3px;
  margin: 0 10px;
}

.x6-widget-stencil-content::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, rgba(50, 197, 255, 0.4), rgba(18, 137, 221, 0.6));
  border-radius: 3px;
  border: 1px solid rgba(50, 197, 255, 0.2);
}

.x6-widget-stencil-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, rgba(50, 197, 255, 0.7), rgba(18, 137, 221, 0.8));
}

.x6-widget-transform {
  margin: -1px 0 0 -1px;
  padding: 0px;
  border: 1px solid #239edd;
}

.x6-widget-transform > div {
  border: 1px solid #239edd;
}

.x6-widget-transform > div:hover {
  background-color: #3dafe4;
}

.x6-widget-transform-active-handle {
  background-color: #3dafe4;
}

.x6-widget-transform-resize {
  border-radius: 0;
}

.x6-widget-selection-inner {
  border: 1px solid #239edd;
}

.x6-widget-selection-box {
  opacity: 0;
}

/* 画布网格和背景优化 */
.task-flow-editor .x6-graph-svg {
  background-color: rgba(18, 137, 221, 0.02);
}

.task-flow-editor .x6-graph-grid {
  opacity: 0.3;
}

/* 防止双击选中文字 */
.task-flow-editor .graph-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.task-flow-editor .x6-node {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 连线交互优化 */
.task-flow-editor .x6-edge {
  cursor: pointer;
}

.task-flow-editor .x6-edge:hover .connection {
  stroke: #32c5ff !important;
  stroke-width: 3 !important;
}

.task-flow-editor .x6-edge.selected .connection {
  stroke: #1890ff !important;
  stroke-width: 4 !important;
}

/* 连线选中框优化 */
.task-flow-editor .x6-widget-selection-box {
  border: 2px solid #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

/* 任务控制样式 - 参照dialog.scss */
.task-flow-editor .task-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.task-flow-editor .task-select {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  background: rgba(18, 137, 221, 0.1);
  border: 1px solid rgba(18, 137, 221, 0.7);
  color: #fff;
  padding: 0 12px;
  border-radius: 4px;
  min-width: 200px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.task-flow-editor .task-select:hover,
.task-flow-editor .task-select:focus {
  border-color: #32c5ff;
  outline: none;
  box-shadow: 0 0 5px rgba(50, 197, 255, 0.3);
}

.task-flow-editor .task-select option {
  background: rgba(0, 20, 60, 0.95);
  color: #fff;
  padding: 8px;
}

.task-flow-editor .task-controls {
  display: flex;
  gap: 8px;
}

.task-flow-editor .add-task-btn,
.task-flow-editor .delete-task-btn,
.task-flow-editor .manage-types-btn {
  background: rgba(18, 137, 221, 0.2);
  border: 1px solid rgba(50, 197, 255, 0.6);
  color: #32c5ff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 90px;
  height: 32px;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-flow-editor .task-list-header {
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(18, 137, 221, 0.5);
  margin-bottom: 10px;
}

.task-flow-editor .task-list-header h4 {
  border-bottom: none !important;
  padding-bottom: 0 !important;
}

.task-flow-editor .add-task-btn:hover,
.task-flow-editor .delete-task-btn:hover,
.task-flow-editor .manage-types-btn:hover {
  background: rgba(50, 197, 255, 0.2);
  border-color: #32c5ff;
  color: #fff;
  box-shadow: 0 0 10px rgba(50, 197, 255, 0.3);
  transform: translateY(-1px);
}

/* 统一对话框样式 - 参照dialog.scss */
.task-flow-editor .dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.task-flow-editor .dialog {
  background-color: rgba(0, 20, 60, 0.95);
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 6px;
  padding: 0;
  min-width: 400px;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(18, 137, 221, 0.3);
  backdrop-filter: blur(8px);
}

.task-flow-editor .dialog h3 {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 0.5px;
  text-shadow: 0 0 10px rgba(50, 197, 255, 0.3);
  padding: 12px 20px;
  margin: 0 0 0 0;
  border-bottom: 1px solid rgba(18, 137, 221, 0.5);
  background: transparent;
}

.task-flow-editor .dialog-body {
  padding: 12px;
  max-height: calc(85vh - 120px);
  overflow: hidden;
}

/* 统一表单样式 - 参照dialog.scss */
.task-flow-editor .form-group {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  min-height: 32px;
}

.task-flow-editor .form-group label {
  color: rgba(255, 255, 255, 0.9);
  text-align: right;
  display: inline-block;
  height: 32px;
  line-height: 32px;
  padding-right: 12px;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
  width: 120px;
  flex-shrink: 0;
}

.task-flow-editor .form-group input,
.task-flow-editor .form-group select {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  background: rgba(18, 137, 221, 0.1);
  border: 1px solid rgba(18, 137, 221, 0.7);
  color: #fff;
  width: 100%;
  padding: 0 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.task-flow-editor .form-group input:hover,
.task-flow-editor .form-group input:focus,
.task-flow-editor .form-group select:hover,
.task-flow-editor .form-group select:focus {
  border-color: #32c5ff;
  outline: none;
  box-shadow: 0 0 5px rgba(50, 197, 255, 0.3);
}

.task-flow-editor .form-group input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.task-flow-editor .form-group input[type="number"] {
  appearance: textfield;
  -moz-appearance: textfield;
  -webkit-appearance: textfield;
}

.task-flow-editor .form-group input[type="number"]::-webkit-inner-spin-button,
.task-flow-editor .form-group input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.task-flow-editor .color-info {
  padding: 8px 12px;
  background: rgba(18, 137, 221, 0.1);
  border: 1px solid rgba(18, 137, 221, 0.3);
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  text-align: center;
}

/* 统一按钮样式 - 参照dialog.scss */
.task-flow-editor .dialog-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  border-top: 1px solid rgba(18, 137, 221, 0.5);
  padding: 8px 20px;
  margin: 0;
  gap: 15px;
}

.task-flow-editor .dialog-actions button {
  min-width: 90px;
  padding: 6px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 15px;
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
  height: 32px;
  line-height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.task-flow-editor .dialog-actions button i {
  font-size: 14px;
}

.task-flow-editor .dialog-actions button:first-child {
  background-color: rgba(18, 137, 221, 0.8);
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
}

.task-flow-editor .dialog-actions button:first-child:hover:not(:disabled) {
  background-color: rgba(18, 137, 221, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(18, 137, 221, 0.3);
}

.task-flow-editor .dialog-actions button:first-child:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(18, 137, 221, 0.2);
}

.task-flow-editor .dialog-actions button:first-child:disabled {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.task-flow-editor .dialog-actions button:last-child {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
}

.task-flow-editor .dialog-actions button:last-child:hover {
  border-color: #32c5ff;
  color: #32c5ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(18, 137, 221, 0.3);
}

.task-flow-editor .dialog-actions button:last-child:active {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(18, 137, 221, 0.2);
}

/* 任务选择对话框样式 */
.task-flow-editor .task-selector-dialog {
  min-width: 650px;
  max-width: 750px;
}











/* 详细任务列表 */
.task-flow-editor .task-list-detailed {
  margin: 8px 0 20px 0;
  max-height: 480px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 4px;
}

/* 自定义滚动条样式 */
.task-flow-editor .task-list-detailed::-webkit-scrollbar {
  width: 4px;
}

.task-flow-editor .task-list-detailed::-webkit-scrollbar-track {
  background: rgba(18, 137, 221, 0.1);
  border-radius: 2px;
}

.task-flow-editor .task-list-detailed::-webkit-scrollbar-thumb {
  background: rgba(50, 197, 255, 0.3);
  border-radius: 2px;
}

.task-flow-editor .task-list-detailed::-webkit-scrollbar-thumb:hover {
  background: rgba(50, 197, 255, 0.5);
}

.task-flow-editor .detailed-task-card {
  padding: 12px 16px;
  margin-bottom: 12px;
  border: 1px solid rgba(18, 137, 221, 0.3);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(18, 137, 221, 0.05);
}

.task-flow-editor .detailed-task-card:hover {
  border-color: rgba(50, 197, 255, 0.6);
  background: rgba(18, 137, 221, 0.1);
}

.task-flow-editor .detailed-task-card.active {
  border-color: #32c5ff;
  background: rgba(50, 197, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(50, 197, 255, 0.2), 0 2px 8px rgba(50, 197, 255, 0.3);
}

.task-flow-editor .detailed-task-card.active .task-indicator {
  box-shadow: 0 0 8px rgba(50, 197, 255, 0.6);
}

.task-flow-editor .detailed-task-card.active .task-name {
  color: #32c5ff;
  text-shadow: 0 0 4px rgba(50, 197, 255, 0.3);
}

.task-flow-editor .detailed-task-card:last-child {
  margin-bottom: 0;
}

.task-flow-editor .task-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.task-flow-editor .task-indicator {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
  margin-right: 12px;
}

.task-flow-editor .task-title-section {
  flex: 1;
}

.task-flow-editor .task-name {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 2px;
}

.task-flow-editor .task-meta {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.task-flow-editor .center-name {
  color: rgba(50, 197, 255, 0.8);
}

.task-flow-editor .task-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.task-flow-editor .detailed-task-card:hover .task-actions {
  opacity: 1;
}

.task-flow-editor .edit-task-btn,
.task-flow-editor .delete-task-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.6);
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  min-height: 28px;
}

.task-flow-editor .edit-task-btn:hover {
  border-color: rgba(50, 197, 255, 0.8);
  color: #32c5ff;
  background: rgba(50, 197, 255, 0.15);
  box-shadow: 0 0 6px rgba(50, 197, 255, 0.4);
}

.task-flow-editor .delete-task-btn:hover {
  border-color: rgba(220, 53, 69, 0.8);
  color: #dc3545;
  background: rgba(220, 53, 69, 0.15);
  box-shadow: 0 0 6px rgba(220, 53, 69, 0.4);
}

.task-flow-editor .edit-task-btn i,
.task-flow-editor .delete-task-btn i {
  font-size: 13px;
}

/* 任务详细参数 */
.task-flow-editor .task-details {
  border-top: 1px solid rgba(18, 137, 221, 0.2);
  padding-top: 6px;
  margin-top: 6px;
}

.task-flow-editor .detail-row {
  display: flex;
  gap: 12px;
  margin-bottom: 3px;
}

.task-flow-editor .detail-row:last-child {
  margin-bottom: 0;
}

.task-flow-editor .detail-group {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;
}

.task-flow-editor .detail-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  margin-right: 3px;
  white-space: nowrap;
  min-width: 40px;
}

.task-flow-editor .detail-value {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 创建新任务区域 */
.task-flow-editor .create-new-section {
  border-top: 1px solid rgba(18, 137, 221, 0.2);
  padding-top: 16px;
  margin-top: 16px;
  text-align: center;
}

.task-flow-editor .create-new-task-btn {
  background: transparent;
  border: 1px dashed rgba(50, 197, 255, 0.6);
  color: rgba(50, 197, 255, 0.8);
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.task-flow-editor .create-new-task-btn:hover {
  border-color: #32c5ff;
  color: #32c5ff;
  background: rgba(50, 197, 255, 0.05);
}

.task-flow-editor .create-new-task-btn i {
  font-size: 12px;
}

/* 空状态样式 */
.task-flow-editor .empty-state {
  padding: 40px 20px;
  text-align: center;
}

.task-flow-editor .empty-content {
  color: rgba(255, 255, 255, 0.6);
}

.task-flow-editor .empty-content i {
  font-size: 48px;
  color: rgba(18, 137, 221, 0.4);
  margin-bottom: 16px;
  display: block;
}

.task-flow-editor .empty-content p {
  font-size: 14px;
  margin: 0 0 20px 0;
}

.task-flow-editor .primary-btn {
  background: linear-gradient(45deg, rgba(18, 137, 221, 0.8), rgba(50, 197, 255, 0.8));
  border: 1px solid #32c5ff;
  color: #fff;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.task-flow-editor .primary-btn:hover {
  background: linear-gradient(45deg, rgba(18, 137, 221, 1), rgba(50, 197, 255, 1));
  transform: translateY(-1px);
}

/* 任务列表管理样式 */
.task-flow-editor .task-list-management {
  padding: 16px;
}

.task-flow-editor .task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-flow-editor .task-list-header h4 {
  margin: 0;
  color: #32c5ff;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid rgba(18, 137, 221, 0.5);
  padding-bottom: 8px;
  flex: 1;
}

.task-flow-editor .empty-table {
  text-align: center;
  padding: 40px 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

/* Element UI 表格样式优化 */
.task-flow-editor .center-table {
  background: transparent !important;
  border: 1px solid rgba(18, 137, 221, 0.3) !important;
}

.task-flow-editor .center-table .el-table__header-wrapper {
  background: rgba(18, 137, 221, 0.1) !important;
}

.task-flow-editor .center-table .el-table__header {
  background: rgba(18, 137, 221, 0.1) !important;
}

.task-flow-editor .center-table .el-table__header th {
  background: rgba(18, 137, 221, 0.1) !important;
  border-bottom: 1px solid rgba(18, 137, 221, 0.3) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 600;
  font-size: 13px;
}

.task-flow-editor .center-table .el-table__body {
  background: transparent !important;
}

.task-flow-editor .center-table .el-table__body tr {
  background: rgba(0, 20, 60, 0.3) !important;
}

.task-flow-editor .center-table .el-table__body tr:hover {
  background: rgba(18, 137, 221, 0.15) !important;
}

.task-flow-editor .center-table .el-table__body td {
  border-bottom: 1px solid rgba(18, 137, 221, 0.2) !important;
  color: rgba(255, 255, 255, 0.85) !important;
  font-size: 13px;
}

.task-flow-editor .center-table .el-button--text {
  color: #32c5ff !important;
  font-size: 12px;
  padding: 4px 8px !important;
}

.task-flow-editor .center-table .el-button--text:hover {
  color: #fff !important;
  background: rgba(50, 197, 255, 0.2) !important;
}

.task-flow-editor .task-form-dialog {
  min-width: 800px;
  max-width: 1000px;
}

.task-flow-editor .form-container {
  padding: 16px;
  max-height: calc(80vh - 140px);
  overflow-y: auto;
}

.task-flow-editor .full-width {
  width: 100%;
}

.task-flow-editor .form-container .el-form-item {
  margin-bottom: 18px;
}

.task-flow-editor .form-container .el-form-item__label {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  font-size: 14px;
  padding-right: 12px;
  line-height: 32px !important;
  height: 32px !important;
  min-width: 120px !important;
  text-align: right !important;
}

.task-flow-editor .form-container .el-form-item__content {
  line-height: 32px !important;
  min-height: 32px !important;
}

.task-flow-editor .form-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 4px;
  background: rgba(18, 137, 221, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.task-flow-editor .form-section h4 {
  margin: 0 0 18px 0;
  color: #32c5ff;
  font-size: 15px;
  font-weight: bold;
  border-bottom: 1px solid rgba(18, 137, 221, 0.5);
  padding-bottom: 8px;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.task-flow-editor .form-section:last-child .el-form-item:last-child {
  margin-bottom: 0;
}

/* 确保两列高度一致 */
.task-flow-editor .form-container .el-row {
  display: flex;
  align-items: stretch;
}

.task-flow-editor .form-container .el-col {
  display: flex;
  flex-direction: column;
}

.task-flow-editor .form-container .el-input__inner {
  background: rgba(18, 137, 221, 0.1) !important;
  border: 1px solid rgba(18, 137, 221, 0.7) !important;
  color: #fff !important;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.task-flow-editor .form-container .el-input__inner:hover,
.task-flow-editor .form-container .el-input__inner:focus {
  border-color: #32c5ff !important;
  box-shadow: 0 0 5px rgba(50, 197, 255, 0.3) !important;
}

.task-flow-editor .form-container .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

.task-flow-editor .form-container .el-input,
.task-flow-editor .form-container .el-select {
  width: 100% !important;
}

.task-flow-editor .form-container .el-input__inner,
.task-flow-editor .form-container .el-select .el-input__inner {
  background: rgba(18, 137, 221, 0.1) !important;
  border: 1px solid rgba(18, 137, 221, 0.7) !important;
  color: #fff !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.task-flow-editor .form-container .el-select-dropdown {
  background: rgba(0, 20, 60, 0.95) !important;
  border: 1px solid rgba(18, 137, 221, 0.5) !important;
}

.task-flow-editor .form-container .el-select-dropdown__item {
  color: #fff !important;
  background: transparent !important;
}

.task-flow-editor .form-container .el-select-dropdown__item.hover,
.task-flow-editor .form-container .el-select-dropdown__item:hover {
  background: rgba(18, 137, 221, 0.2) !important;
  color: #32c5ff !important;
}

.task-flow-editor .form-container .el-select-dropdown__item.selected {
  background: rgba(18, 137, 221, 0.3) !important;
  color: #32c5ff !important;
}

/* 表单控件固定宽度 */
.task-flow-editor .fixed-width {
  width: 320px !important;
}

.task-flow-editor .fixed-width .el-input__inner,
.task-flow-editor .fixed-width.el-input .el-input__inner,
.task-flow-editor .fixed-width.el-select .el-input__inner {
  width: 320px !important;
  box-sizing: border-box !important;
}

.task-flow-editor .form-container .el-input,
.task-flow-editor .form-container .el-select {
  width: 320px !important;
  display: inline-block !important;
}

.task-flow-editor .auto-name-hint {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
  font-style: italic;
}

.task-flow-editor .unit-label {
  margin-left: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

/* 帮助文字样式 */
.task-flow-editor .helper-text {
  font-size: 12px;
  color: #f56c6c;
  margin-top: 4px;
  margin-left: 4px;
}

/* 表单分组标题 */
.task-flow-editor .form-section-title {
  font-size: 16px;
  font-weight: bold;
  color: #32c5ff;
  margin: 24px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(18, 137, 221, 0.5);
  letter-spacing: 0.5px;
}

.task-flow-editor .form-section-title:first-child {
  margin-top: 0;
}

/* 操作符列表样式 */
.task-flow-editor .operator-list {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 6px;
  background: rgba(18, 137, 221, 0.05);
}

.task-flow-editor .operator-list-header {
  font-size: 14px;
  font-weight: bold;
  color: #32c5ff;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(18, 137, 221, 0.3);
}

.task-flow-editor .operator-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid rgba(18, 137, 221, 0.4);
  border-radius: 4px;
  background: rgba(0, 20, 60, 0.3);
  transition: all 0.3s ease;
}

.task-flow-editor .operator-item:hover {
  background: rgba(0, 20, 60, 0.5);
  border-color: rgba(18, 137, 221, 0.7);
}

.task-flow-editor .operator-item:last-child {
  margin-bottom: 0;
}

.task-flow-editor .operator-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
  margin-right: 12px;
  min-width: 80px;
  white-space: nowrap;
}

.task-flow-editor .operator-input {
  width: 120px !important;
}

.task-flow-editor .operator-input .el-input__inner {
  background: rgba(18, 137, 221, 0.15) !important;
  border: 1px solid rgba(18, 137, 221, 0.6) !important;
  color: #fff !important;
  height: 32px;
  line-height: 32px;
  padding: 0 12px;
}

.task-flow-editor .operator-input .el-input__inner:focus {
  border-color: #32c5ff !important;
  box-shadow: 0 0 4px rgba(50, 197, 255, 0.4) !important;
}

/* 开关组件样式 */
.task-flow-editor .form-container .el-switch {
  display: inline-block;
}

.task-flow-editor .form-container .el-switch__core {
  background: rgba(18, 137, 221, 0.3) !important;
  border-color: rgba(18, 137, 221, 0.7) !important;
}

.task-flow-editor .form-container .el-switch.is-checked .el-switch__core {
  background: #32c5ff !important;
  border-color: #32c5ff !important;
}

.task-flow-editor .form-container .el-switch__label {
  color: rgba(255, 255, 255, 0.8) !important;
}

.task-flow-editor .form-container .el-switch__label.is-active {
  color: #32c5ff !important;
}

/* 属性编辑对话框样式 - 参照dialog.scss */
.task-flow-editor .properties-dialog {
  min-width: 700px;
  max-width: 900px;
}

.task-flow-editor .properties-form {
  max-height: calc(80vh - 160px);
  overflow-y: auto;
  padding: 0 10px;
}

.task-flow-editor .form-section {
  margin-bottom: 20px;
  padding: 12px;
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 4px;
  background: rgba(18, 137, 221, 0.05);
}

.task-flow-editor .form-section h4 {
  margin: 0 0 16px 0;
  color: #32c5ff;
  font-size: 15px;
  font-weight: bold;
  border-bottom: 1px solid rgba(18, 137, 221, 0.5);
  padding-bottom: 8px;
  letter-spacing: 0.5px;
}

.task-flow-editor .form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.task-flow-editor .form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.task-flow-editor .operator-item {
  padding: 12px;
  border: 1px solid rgba(18, 137, 221, 0.5);
  border-radius: 4px;
  margin-bottom: 8px;
  background: rgba(0, 20, 60, 0.4);
}

.task-flow-editor .add-btn {
  background-color: rgba(18, 137, 221, 0.8);
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
  padding: 6px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 80px;
}

.task-flow-editor .add-btn:hover {
  background-color: rgba(18, 137, 221, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(18, 137, 221, 0.3);
}

.task-flow-editor .remove-btn {
  background: rgba(220, 53, 69, 0.8);
  border: 1px solid rgba(220, 53, 69, 1);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-left: 8px;
  transition: all 0.3s ease;
}

.task-flow-editor .remove-btn:hover {
  background: rgba(220, 53, 69, 1);
  transform: translateY(-1px);
}

/* 只读输入框样式 */
.task-flow-editor .readonly-input {
  background-color: rgba(18, 137, 221, 0.1) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid rgba(18, 137, 221, 0.4) !important;
  cursor: not-allowed;
}

.task-flow-editor .readonly-select {
  background-color: rgba(18, 137, 221, 0.1) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid rgba(18, 137, 221, 0.4) !important;
  cursor: not-allowed;
}

/* 任务管理按钮样式 */
.task-flow-editor .header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-flow-editor .task-management-btn {
  background: rgba(18, 137, 221, 0.2);
  border: 1px solid rgba(50, 197, 255, 0.6);
  color: #32c5ff;
  padding: 6px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  height: 32px;
}

.task-flow-editor .task-management-btn:hover {
  background: rgba(50, 197, 255, 0.2);
  border-color: #32c5ff;
  color: #fff;
  box-shadow: 0 0 10px rgba(50, 197, 255, 0.3);
  transform: translateY(-1px);
}

.task-flow-editor .task-management-btn i {
  font-size: 16px;
}



/* 节点属性编辑对话框样式 */
.task-flow-editor .node-properties-dialog {
  min-width: 500px;
  max-width: 700px;
}

/* 任务管理对话框样式 */
.task-flow-editor .task-management-dialog {
  min-width: 800px;
  max-width: 1000px;
}

.task-flow-editor .task-management-content {
  width: 100%;
}

.task-flow-editor .section-header-mini {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.task-flow-editor .section-title-mini {
  color: #32c5ff;
  font-size: 16px;
  font-weight: bold;
}

.task-flow-editor .add-btn-mini {
  background: rgba(18, 137, 221, 0.8);
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.task-flow-editor .add-btn-mini:hover {
  background: rgba(18, 137, 221, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(18, 137, 221, 0.3);
}



/* 前置任务选择器样式 - 适配深蓝主题 */
.task-flow-editor .prerequisite-select .el-input__inner {
  height: 32px !important;
  line-height: 32px !important;
  font-size: 14px;
  background: rgba(18, 137, 221, 0.1);
  border: 1px solid rgba(18, 137, 221, 0.7);
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.9);
  padding: 0 12px;
  transition: all 0.3s ease;
  min-height: 32px !important;
  max-height: 32px !important;
}

.task-flow-editor .prerequisite-select .el-input__inner:hover {
  border-color: #32c5ff;
}

.task-flow-editor .prerequisite-select .el-input__inner:focus {
  border-color: #32c5ff;
  outline: none;
  box-shadow: 0 0 5px rgba(50, 197, 255, 0.3);
}

.task-flow-editor .prerequisite-select .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 多选标签样式 */
.task-flow-editor .prerequisite-select .el-tag {
  background-color: rgba(18, 137, 221, 0.3) !important;
  border: 1px solid rgba(18, 137, 221, 0.8) !important;
  color: #32c5ff !important;
  border-radius: 3px;
  font-size: 12px;
  padding: 2px 6px;
  margin: 2px;
  font-weight: 500;
  height: auto;
  line-height: 1.4;
  display: inline-flex;
  align-items: center;
}

.task-flow-editor .prerequisite-select .el-tag .el-tag__close {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  transition: all 0.2s ease;
}

.task-flow-editor .prerequisite-select .el-tag .el-tag__close:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-radius: 50%;
}

/* 确保标签文本正确显示 */
.task-flow-editor .prerequisite-select .el-tag span {
  color: #32c5ff !important;
  font-size: 12px;
  font-weight: 500;
}

/* 多选输入框内的标签容器 */
.task-flow-editor .prerequisite-select .el-select__tags {
  max-width: calc(100% - 30px);
  overflow: visible;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

.task-flow-editor .prerequisite-select .el-select__tags .el-tag {
  margin: 2px 4px 2px 0;
  max-width: none;
  height: 20px;
  line-height: 18px;
}

/* 确保选择器容器高度固定 */
.task-flow-editor .prerequisite-select .el-input {
  height: 32px !important;
}

.task-flow-editor .prerequisite-select .el-input--small .el-input__inner {
  height: 32px !important;
}

/* 下拉箭头样式 */
.task-flow-editor .prerequisite-select .el-input__suffix .el-input__suffix-inner .el-select__caret {
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.task-flow-editor .prerequisite-select .el-input__suffix .el-input__suffix-inner .el-select__caret:hover {
  color: #32c5ff;
}

/* 下拉选项样式 - 使用更通用的选择器 */
.el-select-dropdown .el-select-dropdown__item {
  background-color: rgba(0, 20, 60, 0.95) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  border-bottom: 1px solid rgba(18, 137, 221, 0.2);
  padding: 8px 16px;
  font-size: 13px;
  line-height: 20px;
  height: 36px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.el-select-dropdown .el-select-dropdown__item:hover {
  background-color: rgba(18, 137, 221, 0.3) !important;
  color: #32c5ff !important;
}

.el-select-dropdown .el-select-dropdown__item.selected {
  background-color: rgba(18, 137, 221, 0.5) !important;
  color: #32c5ff !important;
  font-weight: 500;
}

/* 下拉面板背景 */
.el-select-dropdown {
  background-color: rgba(0, 20, 60, 0.95) !important;
  border: 1px solid rgba(18, 137, 221, 0.5) !important;
  border-radius: 4px !important;
  box-shadow: 0 0 20px rgba(18, 137, 221, 0.3) !important;
  backdrop-filter: blur(8px);
}

.el-select-dropdown .el-scrollbar__view {
  background-color: transparent !important;
}







/* 任务管理表格样式 - 使用与计算中心表格相同的样式 */
.task-flow-editor .center-table {
  margin-bottom: 15px;
}

.task-flow-editor .edit-btn-mini,
.task-flow-editor .delete-btn-mini {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.6);
  padding: 4px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  margin-right: 6px;
}

.task-flow-editor .edit-btn-mini:hover {
  border-color: rgba(50, 197, 255, 0.8);
  color: #32c5ff;
  background: rgba(50, 197, 255, 0.15);
}

.task-flow-editor .delete-btn-mini:hover {
  border-color: rgba(220, 53, 69, 0.8);
  color: #dc3545;
  background: rgba(220, 53, 69, 0.15);
}



.task-flow-editor .create-first-btn {
  background: linear-gradient(45deg, rgba(18, 137, 221, 0.8), rgba(50, 197, 255, 0.8));
  border: 1px solid #32c5ff;
  color: #fff;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.task-flow-editor .create-first-btn:hover {
  background: linear-gradient(45deg, rgba(18, 137, 221, 1), rgba(50, 197, 255, 1));
  transform: translateY(-1px);
}



/* disabled输入框灰度处理 */
.task-flow-editor .el-input.is-disabled .el-input__inner,
.task-flow-editor .el-select.is-disabled .el-input__inner {
  filter: grayscale(100%);
  opacity: 0.6;
}

/* 连接桩样式优化 - 保持美观的同时增强可操作性 */
.task-flow-editor .x6-port-body {
  transition: all 0.2s ease !important;
  cursor: crosshair !important;
}

.task-flow-editor .x6-port-body:hover {
  stroke: #32c5ff !important;
  stroke-width: 2 !important;
  fill: #32c5ff !important;
  r: 6 !important;
  filter: drop-shadow(0 0 4px rgba(50, 197, 255, 0.5)) !important;
}

/* 增加连接桩的点击区域 */
.task-flow-editor .x6-port {
  cursor: crosshair !important;
}

.task-flow-editor .x6-port::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  background: transparent;
  pointer-events: all;
}

/* 连接线样式优化 */
.task-flow-editor .x6-edge-wrap {
  stroke-width: 10 !important;
  stroke: transparent !important;
  cursor: pointer !important;
}

/* 磁性吸附效果增强 */
.task-flow-editor .x6-port-body.magnet-available {
  stroke: #32c5ff !important;
  stroke-width: 2 !important;
  fill: rgba(50, 197, 255, 0.2) !important;
  r: 6 !important;
  animation: magnetPulse 1s ease-in-out infinite alternate !important;
}

.task-flow-editor .x6-port-body.magnet-adsorbed {
  stroke: #32c5ff !important;
  stroke-width: 3 !important;
  fill: #32c5ff !important;
  r: 8 !important;
  filter: drop-shadow(0 0 8px rgba(50, 197, 255, 0.8)) !important;
}

@keyframes magnetPulse {
  from {
    opacity: 0.6;
    transform: scale(1);
  }
  to {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* 连线时的节点高亮 */
.task-flow-editor .x6-node.connecting {
  filter: drop-shadow(0 0 10px rgba(50, 197, 255, 0.5)) !important;
}
</style>
