import request from '@/utils/request'
import request2 from '@/utils/request2'
export function getJob (params) {
  return request({
    url: '/api/v1/tasks',
    method: 'get',
    params
  })
}
export function jobDetail (params) {
  return request({
    url: `/api/v1/tasks/${params.id}`,
    method: 'get',
    params
  })
}
export function getCenter () {
  return request({
    url: '/api/v1/locations',
    method: 'get'
  })
}

export function getJobAnalysis (data) {
  return request({
    url: '/api/v1/job/analysis',
    method: 'post',
    data
  })
}
