<template>
  <div class="simulation-overview">
    <div class="overview-header">
      <div class="header-icon">
        <i class="el-icon-data-analysis"></i>
      </div>
      <h3 class="header-title">仿真任务总览</h3>
    </div>

    <div class="overview-content">
      <!-- 统计卡片区域 -->
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-icon total">
            <i class="el-icon-document"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ taskStats.totalTasks }}</div>
            <div class="stat-label">总任务数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon running">
            <i class="el-icon-loading"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ taskStats.runningTasks }}</div>
            <div class="stat-label">运行中</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon completed">
            <i class="el-icon-check"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ taskStats.completedTasks }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon pending">
            <i class="el-icon-time"></i>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ taskStats.pendingTasks }}</div>
            <div class="stat-label">等待中</div>
          </div>
        </div>
      </div>

      <!-- 环形进度图 -->
      <div class="progress-section">
        <div class="progress-chart">
          <div ref="progressChart" class="chart-container"></div>
        </div>
        <div class="progress-info">
          <div class="progress-text">
            <div class="progress-percentage">{{ completionRate }}%</div>
            <div class="progress-label">完成率</div>
          </div>
        </div>
      </div>

      <!-- 趋势图表 -->
      <div class="trend-section">
        <div class="trend-header">
          <h4>任务执行趋势</h4>
        </div>
        <div ref="trendChart" class="trend-chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'SimulationOverview',

  props: {
    // 任务统计数据
    taskStats: {
      type: Object,
      default: () => ({
        totalTasks: 0,
        runningTasks: 0,
        completedTasks: 0,
        pendingTasks: 0
      })
    },

    // 趋势数据
    trendData: {
      type: Object,
      default: () => ({
        timeLabels: [],
        submitData: [],
        completeData: []
      })
    },

    // 当前帧数
    currentFrame: {
      type: Number,
      default: 0
    }
  },

  data () {
    return {
      progressChart: null,
      trendChart: null
    }
  },

  computed: {
    // 完成率
    completionRate () {
      if (this.taskStats.totalTasks === 0) return 0
      return Math.round((this.taskStats.completedTasks / this.taskStats.totalTasks) * 100)
    }
  },

  watch: {
    taskStats: {
      handler () {
        this.updateProgressChart()
      },
      deep: true
    },

    trendData: {
      handler () {
        this.updateTrendChart()
      },
      deep: true
    },

    currentFrame () {
      this.updateCharts()
    }
  },

  mounted () {
    this.initCharts()
  },

  beforeDestroy () {
    if (this.progressChart) {
      this.progressChart.dispose()
    }
    if (this.trendChart) {
      this.trendChart.dispose()
    }
  },

  methods: {
    /**
     * 初始化图表
     */
    initCharts () {
      this.$nextTick(() => {
        this.initProgressChart()
        this.initTrendChart()
      })
    },

    /**
     * 初始化进度图表
     */
    initProgressChart () {
      if (!this.$refs.progressChart) return

      this.progressChart = echarts.init(this.$refs.progressChart)
      this.updateProgressChart()
    },

    /**
     * 初始化趋势图表
     */
    initTrendChart () {
      if (!this.$refs.trendChart) return

      this.trendChart = echarts.init(this.$refs.trendChart)
      this.updateTrendChart()
    },

    /**
     * 更新进度图表
     */
    updateProgressChart () {
      if (!this.progressChart) return

      const option = {
        series: [
          {
            type: 'pie',
            radius: ['60%', '80%'],
            center: ['50%', '50%'],
            startAngle: 90,
            data: [
              {
                value: this.taskStats.completedTasks,
                name: '已完成',
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: '#52c41a' },
                      { offset: 1, color: '#73d13d' }
                    ]
                  }
                }
              },
              {
                value: this.taskStats.totalTasks - this.taskStats.completedTasks,
                name: '未完成',
                itemStyle: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              }
            ],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            emphasis: {
              disabled: true
            }
          }
        ]
      }

      this.progressChart.setOption(option)
    },

    /**
     * 更新趋势图表
     */
    updateTrendChart () {
      if (!this.trendChart) return

      const option = {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.trendData.timeLabels || [],
          axisLabel: {
            color: '#fff',
            fontSize: 10
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 10
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)'
            }
          }
        },
        series: [
          {
            name: '提交任务',
            type: 'line',
            data: this.trendData.submitData || [],
            smooth: true,
            lineStyle: {
              color: '#32c5ff',
              width: 2
            },
            itemStyle: {
              color: '#32c5ff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(50, 197, 255, 0.3)' },
                  { offset: 1, color: 'rgba(50, 197, 255, 0.1)' }
                ]
              }
            }
          },
          {
            name: '完成任务',
            type: 'line',
            data: this.trendData.completeData || [],
            smooth: true,
            lineStyle: {
              color: '#52c41a',
              width: 2
            },
            itemStyle: {
              color: '#52c41a'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
                  { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
                ]
              }
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#32c5ff',
          textStyle: {
            color: '#fff'
          }
        }
      }

      this.trendChart.setOption(option)
    },

    /**
     * 更新所有图表
     */
    updateCharts () {
      this.updateProgressChart()
      this.updateTrendChart()
    }
  }
}
</script>

<style scoped>
.simulation-overview {
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.9) 0%, rgba(0, 30, 60, 0.9) 100%);
  border: 1px solid rgba(50, 197, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.overview-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(50, 197, 255, 0.2);
}

.header-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #32c5ff 0%, #1890ff 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  box-shadow: 0 4px 12px rgba(50, 197, 255, 0.3);
}

.header-icon i {
  font-size: 20px;
  color: #fff;
}

.header-title {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.overview-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(50, 197, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(50, 197, 255, 0.2);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.stat-icon.total {
  background: linear-gradient(135deg, #32c5ff 0%, #1890ff 100%);
}

.stat-icon.running {
  background: linear-gradient(135deg, #ff9500 0%, #ff7300 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
}

.stat-icon i {
  font-size: 16px;
  color: #fff;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}

.progress-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.progress-chart {
  position: relative;
  width: 120px;
  height: 120px;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.progress-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-percentage {
  font-size: 24px;
  font-weight: bold;
  color: #32c5ff;
  line-height: 1;
}

.progress-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
}

.trend-section {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.trend-header {
  margin-bottom: 15px;
}

.trend-header h4 {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  margin: 0;
}

.trend-chart {
  width: 100%;
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-value {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .overview-header {
    flex-direction: column;
    text-align: center;
  }

  .header-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
