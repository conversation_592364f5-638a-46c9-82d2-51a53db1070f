<template>
  <div class="right-sidebar">
    <!-- 标签页导航 -->
    <div class="sidebar-tabs">
      <div class="tabs-header">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          :class="['tab-item', { active: activeTab === tab.key }]"
          @click="switchTab(tab.key)"
        >
          <i :class="tab.icon"></i>
          <span>{{ tab.label }}</span>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="sidebar-content">
      <!-- 提交任务量分析 -->
      <div v-show="activeTab === 'submit'" class="content-panel">
        <div class="panel-header">
          <h4>提交任务量分析</h4>
        </div>
        <div class="chart-container">
          <div ref="submitChart" class="chart"></div>
        </div>
        <div class="chart-summary">
          <div class="summary-item">
            <span class="label">当前值:</span>
            <span class="value">{{ currentSubmitValue }}</span>
          </div>
          <div class="summary-item">
            <span class="label">峰值:</span>
            <span class="value">{{ maxSubmitValue }}</span>
          </div>
        </div>
      </div>

      <!-- 等待时长分析 -->
      <div v-show="activeTab === 'waiting'" class="content-panel">
        <div class="panel-header">
          <h4>任务等待时长分析</h4>
        </div>
        <div class="chart-container">
          <div ref="waitingChart" class="chart"></div>
        </div>
        <div class="chart-summary">
          <div class="summary-item">
            <span class="label">当前等待:</span>
            <span class="value">{{ currentWaitingTime }}h</span>
          </div>
          <div class="summary-item">
            <span class="label">最长等待:</span>
            <span class="value">{{ maxWaitingTime }}h</span>
          </div>
        </div>
      </div>

      <!-- 资源利用率分析 -->
      <div v-show="activeTab === 'resource'" class="content-panel">
        <div class="panel-header">
          <h4>资源利用率分析</h4>
        </div>
        <div class="chart-container">
          <div ref="resourceChart" class="chart"></div>
        </div>
        <div class="chart-summary">
          <div class="summary-item">
            <span class="label">当前利用率:</span>
            <span class="value">{{ currentResourceUsage }}%</span>
          </div>
          <div class="summary-item">
            <span class="label">峰值利用率:</span>
            <span class="value">{{ maxResourceUsage }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'RightSidebar',

  props: {
    // 图表数据
    chartData: {
      type: Object,
      default: () => ({
        submitData: [],
        waitingData: [],
        resourceData: []
      })
    },

    // 当前帧数
    currentFrame: {
      type: Number,
      default: 0
    }
  },

  data () {
    return {
      // 当前活跃标签
      activeTab: 'submit',

      // 标签页配置
      tabs: [
        { key: 'submit', label: '提交量', icon: 'el-icon-upload2' },
        { key: 'waiting', label: '等待时长', icon: 'el-icon-time' },
        { key: 'resource', label: '资源利用', icon: 'el-icon-cpu' }
      ],

      // 图表实例
      charts: {
        submit: null,
        waiting: null,
        resource: null
      }
    }
  },

  computed: {
    // 当前提交任务量
    currentSubmitValue () {
      const data = this.chartData.submitData
      if (!data || data.length === 0) return 0
      const currentData = data[this.currentFrame] || []
      return currentData.reduce((sum, val) => sum + val, 0)
    },

    // 最大提交任务量
    maxSubmitValue () {
      const data = this.chartData.submitData
      if (!data || data.length === 0) return 0
      return Math.max(...data.map(frame => frame.reduce((sum, val) => sum + val, 0)))
    },

    // 当前等待时间
    currentWaitingTime () {
      const data = this.chartData.waitingData
      if (!data || data.length === 0) return 0
      const currentData = data[this.currentFrame] || []
      const avg = currentData.reduce((sum, val) => sum + val, 0) / currentData.length
      return avg.toFixed(1)
    },

    // 最长等待时间
    maxWaitingTime () {
      const data = this.chartData.waitingData
      if (!data || data.length === 0) return 0
      const max = Math.max(...data.flat())
      return max.toFixed(1)
    },

    // 当前资源利用率
    currentResourceUsage () {
      const data = this.chartData.resourceData
      if (!data || data.length === 0) return 0
      const currentData = data[this.currentFrame] || []
      const avg = currentData.reduce((sum, val) => sum + val, 0) / currentData.length
      return (avg * 100).toFixed(1)
    },

    // 最大资源利用率
    maxResourceUsage () {
      const data = this.chartData.resourceData
      if (!data || data.length === 0) return 0
      const max = Math.max(...data.flat())
      return (max * 100).toFixed(1)
    }
  },

  watch: {
    activeTab (newTab) {
      this.$nextTick(() => {
        this.initChart(newTab)
      })
    },

    chartData: {
      handler () {
        this.updateAllCharts()
      },
      deep: true
    },

    currentFrame () {
      this.updateAllCharts()
    }
  },

  mounted () {
    this.$nextTick(() => {
      this.initChart(this.activeTab)
    })
  },

  beforeDestroy () {
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
  },

  methods: {
    /**
     * 切换标签页
     */
    switchTab (tabKey) {
      this.activeTab = tabKey
      this.$emit('tab-change', tabKey)
    },

    /**
     * 初始化图表
     */
    initChart (type) {
      const refName = `${type}Chart`
      if (!this.$refs[refName]) return

      if (this.charts[type]) {
        this.charts[type].dispose()
      }

      this.charts[type] = echarts.init(this.$refs[refName])
      this.updateChart(type)
    },

    /**
     * 更新图表
     */
    updateChart (type) {
      const chart = this.charts[type]
      if (!chart) return

      let option = {}

      switch (type) {
        case 'submit':
          option = this.createSubmitChartOption()
          break
        case 'waiting':
          option = this.createWaitingChartOption()
          break
        case 'resource':
          option = this.createResourceChartOption()
          break
      }

      chart.setOption(option, true)
    },

    /**
     * 创建提交任务量图表配置
     */
    createSubmitChartOption () {
      const data = this.chartData.submitData
      if (!data || data.length === 0) return this.createEmptyOption()

      const currentData = data[this.currentFrame] || []
      const xData = currentData.map((_, index) => `节点${index + 1}`)

      return {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#fff',
            fontSize: 10,
            rotate: 45
          },
          axisLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 10
          },
          axisLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
          },
          splitLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.1)' }
          }
        },
        series: [
          {
            type: 'bar',
            data: currentData,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#32c5ff' },
                  { offset: 1, color: '#1890ff' }
                ]
              }
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#32c5ff',
          textStyle: { color: '#fff' }
        }
      }
    },

    /**
     * 创建等待时长图表配置
     */
    createWaitingChartOption () {
      const data = this.chartData.waitingData
      if (!data || data.length === 0) return this.createEmptyOption()

      const currentData = data[this.currentFrame] || []
      const xData = currentData.map((_, index) => `节点${index + 1}`)

      return {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#fff',
            fontSize: 10,
            rotate: 45
          },
          axisLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 10,
            formatter: (value) => value + 'h'
          },
          axisLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
          },
          splitLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.1)' }
          }
        },
        series: [
          {
            type: 'bar',
            data: currentData,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#faad14' },
                  { offset: 1, color: '#d48806' }
                ]
              }
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#faad14',
          textStyle: { color: '#fff' }
        }
      }
    },

    /**
     * 创建资源利用率图表配置
     */
    createResourceChartOption () {
      const data = this.chartData.resourceData
      if (!data || data.length === 0) return this.createEmptyOption()

      const currentData = data[this.currentFrame] || []
      const processedData = currentData.map(val => (val * 100).toFixed(1))
      const xData = currentData.map((_, index) => `节点${index + 1}`)

      return {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#fff',
            fontSize: 10,
            rotate: 45
          },
          axisLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
          }
        },
        yAxis: {
          type: 'value',
          max: 100,
          axisLabel: {
            color: '#fff',
            fontSize: 10,
            formatter: (value) => value + '%'
          },
          axisLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
          },
          splitLine: {
            lineStyle: { color: 'rgba(255, 255, 255, 0.1)' }
          }
        },
        series: [
          {
            type: 'bar',
            data: processedData,
            itemStyle: {
              color: (params) => {
                const value = params.value
                if (value < 30) return '#52c41a'
                if (value < 70) return '#faad14'
                return '#ff4d4f'
              }
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#52c41a',
          textStyle: { color: '#fff' }
        }
      }
    },

    /**
     * 创建空图表配置
     */
    createEmptyOption () {
      return {
        title: {
          text: '暂无数据',
          textStyle: { color: '#fff', fontSize: 14 },
          left: 'center',
          top: 'center'
        }
      }
    },

    /**
     * 更新所有图表
     */
    updateAllCharts () {
      Object.keys(this.charts).forEach(type => {
        if (this.charts[type]) {
          this.updateChart(type)
        }
      })
    }
  }
}
</script>

<style scoped>
.right-sidebar {
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.9) 0%, rgba(0, 30, 60, 0.9) 100%);
  border: 1px solid rgba(50, 197, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.sidebar-tabs {
  margin-bottom: 20px;
}

.tabs-header {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
}

.tab-item:hover {
  background: rgba(50, 197, 255, 0.1);
  border-color: rgba(50, 197, 255, 0.3);
  color: #fff;
}

.tab-item.active {
  background: rgba(50, 197, 255, 0.2);
  border-color: #32c5ff;
  color: #32c5ff;
  box-shadow: 0 2px 8px rgba(50, 197, 255, 0.3);
}

.tab-item i {
  font-size: 16px;
  margin-bottom: 4px;
}

.tab-item span {
  font-size: 11px;
  font-weight: 500;
}

.sidebar-content {
  flex: 1;
  overflow: hidden;
}

.content-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(50, 197, 255, 0.2);
}

.panel-header h4 {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  margin: 0;
}

.chart-container {
  flex: 1;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 15px;
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.chart-summary {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.summary-item .value {
  color: #32c5ff;
  font-size: 14px;
  font-weight: bold;
}
</style>
