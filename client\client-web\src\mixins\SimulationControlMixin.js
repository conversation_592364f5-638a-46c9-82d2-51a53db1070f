/**
 * 仿真控制混入 - 提供仿真动画控制相关的方法
 */
import TaskDataService from '@/services/TaskDataService'

export default {
  data () {
    return {
      // 控制仿真的状态变量
      timer: null,
      count: 0,
      nowCount: 0,
      percentage: 0,
      disable: false,
      buttonShow: true,
      showButtom: true,
      showPer: true,
      Stop: true,
      showStart: false,
      taskIdRes: null,
      compareIdRes: null,
      dataAlreadyLoaded: false,
      temp: null
    }
  },
  methods: {
    /**
     * 开始或重新开始动画
     */
    start () {
      this.prepareForStart()

      if (this.taskId !== 0) {
        this.getJobData().then(({ taskIdRes, compareIdRes }) => {
          if (taskIdRes) {
            this.processJobData(taskIdRes, compareIdRes)
            // 设置数据已加载标记
            this.dataAlreadyLoaded = true

            // 确保地图更新：等待父组件数据传递和处理完成后，通知地图组件刷新
            this.$nextTick(() => {
              if (this.$refs.chinaMap) {
                console.log('数据已加载完成，通知地图组件刷新')
                // 由于已经通过props传递了数据，不需要再次请求，直接通知地图组件刷新
                this.$refs.chinaMap._preventReinitOnNextUpdate = false

                // 只有在没有传递taskResponse时，才需要调用getJobDetail
                if (!this.taskIdRes) {
                  // 强制重新获取任务数据并渲染地图
                  this.$refs.chinaMap.getJobDetail(this.taskId)
                }
              }
            })
          }
        })
      }
    },

    /**
     * 准备开始
     */
    prepareForStart () {
      this.disable = true
      clearInterval(this.timer)
      this.count = 0
      // 重置数据加载状态
      this.dataAlreadyLoaded = false

      // 重要：使用nextTick机制确保DOM更新后再修改状态
      this.$nextTick(() => {
        // 如果chinaMap组件存在，确保它的状态保持
        if (this.$refs.chinaMap) {
          // 设置chinaMap的内部状态，但不重建地图
          this.$refs.chinaMap.stopped = false
        }

        // 使用空数据结构，仅保持图表框架
        this.resetChartData()

        // 在设置Stop状态前先绘制图表
        this.drawCharts()

        // 最后才设置Stop状态
        this.Stop = false
        this.buttonShow = true
      })
    },

    /**
     * 暂停仿真
     */
    stop () {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.nowCount = this.count
      this.buttonShow = false
      this.Stop = true
    },

    /**
     * 继续仿真
     */
    goOn () {
      if (this.count == this.total) {
        return
      }

      // 设置基本状态标记
      this.Stop = false
      this.buttonShow = true
      this.count = this.nowCount

      // 通知地图组件恢复动画
      if (this.$refs.chinaMap) {
        console.log('通知地图组件恢复动画')
      }

      // 检查是否已经有加载的数据
      if (this.dataAlreadyLoaded && this.temp && this.Data.xAxis.length > 0) {
        console.log('使用已缓存的数据恢复动画，不需要重新请求')

        // 立即更新当前帧数据 - 这是关键修改部分
        const currentIndex = this.count

        // 立即更新环图数据，确保继续时环图立即更新
        if (this.taskIdRes && this.taskIdRes.CenterInfoToWebList) {
          const centerInfoList = this.taskIdRes.CenterInfoToWebList
          let historySubmitJob = 0
          let historyCompleteJob = 0

          // 从当前快照收集数据
          centerInfoList.forEach(center => {
            if (center.SnapshotInfoToWebList &&
                center.SnapshotInfoToWebList[currentIndex]) {
              historySubmitJob += center.SnapshotInfoToWebList[currentIndex].HistorySubmitJob
              historyCompleteJob += center.SnapshotInfoToWebList[currentIndex].HistoryCompleteJob
            }
          })

          // 更新环图数据
          this.updateCircleData(historySubmitJob, historyCompleteJob)
        }

        // 根据当前选择的任务类型立即更新右侧图表
        this.updateTaskDataByType(this.count)

        // 立即重绘右侧图表
        this.drawCharts()

        // 清除任何可能存在的定时器
        clearInterval(this.timer)

        // 从当前帧重启定时器
        this.startAnimationFromCurrentFrame()
      } else {
        // 没有缓存数据，使用原来的方法
        this.prepareForResume()

        if (this.taskId !== 0) {
          TaskDataService.getJobData(this.taskId, this.compareId, this.interval).then(({ taskIdRes, compareIdRes }) => {
            if (taskIdRes) {
              this.processJobData(taskIdRes, compareIdRes)
              this.dataAlreadyLoaded = true
            }
          })
        }
      }
    },

    /**
     * 从当前帧开始动画
     */
    startAnimationFromCurrentFrame () {
      const that = this
      this.timer = setInterval(() => {
        that.count++
        if (that.count >= that.total) {
          clearInterval(that.timer)
          that.count = that.total
          return
        }

        // 更新数据
        // 更新折线图数据
        if (that.temp && that.count < that.temp.length) {
          that.Data.yAxis[that.count] = that.temp[that.count]
        }

        // 更新时间信息
        if (that.Data.xAxis && that.count < that.Data.xAxis.length) {
          that.updateTimeInfo(that.count)
        }

        // 更新环图数据
        that.updateCircleDataFromSnapshot(that.count)

        // 更新右侧任务统计图
        that.updateTaskDataByType(that.count)

        // 重绘图表
        that.drawCharts()
      }, 2000)
    },

    /**
     * 准备恢复
     */
    prepareForResume () {
      this.Stop = false
      this.buttonShow = true
      this.count = this.nowCount

      // 重置图表数据，避免显示旧数据
      this.resetChartData()
    },

    /**
     * 重置图表和任务数据
     */
    resetChartData () {
      // 由子组件实现
    },

    /**
     * 绘制所有图表
     */
    drawCharts () {
      // 由子组件实现
    },

    /**
     * 更新环形图数据
     */
    updateCircleData (totalNum, execNum) {
      // 由子组件实现
    },

    /**
     * 从快照更新环形图数据
     */
    updateCircleDataFromSnapshot (index) {
      // 由子组件实现
    },

    /**
     * 更新时间信息
     */
    updateTimeInfo (index) {
      // 由子组件实现
    },

    /**
     * 根据任务类型更新任务数据
     */
    updateTaskDataByType (index) {
      // 由子组件实现
    },

    /**
     * 处理进度条变化
     */
    changeProgress () {
      this.count = Math.floor((this.percentage / 100) * this.total)
      this.nowCount = this.count
    },

    /**
     * 处理时间间隔变化
     */
    changeInterval () {
      this.$store.commit('changeInterval', this.value)
      this.Stop = true
      this.start()
    },

    /**
     * 格式化提示文本
     */
    formatTooltip (val) {
      return val + '%'
    }
  }
}
