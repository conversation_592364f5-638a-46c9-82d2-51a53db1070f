<template>
  <div style="width: 100%; height: 535px">
    <el-table
      :data="newData"
      border
      style="width: 100%"
      align="center"
      size="mini"
      id="dbM"
      class="customer-table"
      empty-text="暂无数据"
      @row-click="handleRowClick"
    >
      <el-table-column
        :label="menuData[0].name"
        :prop="menuData[0].prop"
        :show-overflow-tooltip="true"
        width="40"
      >
      </el-table-column>
      <el-table-column
        :label="menuData[1].name"
        :prop="menuData[1].prop"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        :label="menuData[2].name"
        :prop="menuData[2].prop"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column label="仿真状态" align="center">
        <template slot-scope="scope">
          <div class="running" v-if="scope.row.CompletedFlag == true"></div>
          <div class="pending" v-if="scope.row.CompletedFlag != true"></div>
        </template>
      </el-table-column>
      <el-table-column
        :label="menuData[4].name"
        :prop="menuData[4].prop"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
        <el-table-column
        :label="menuData[5].name"
        :prop="menuData[5].prop"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <slot name="footerTable"></slot>
    </el-table>
  </div>
</template>

<script>
let _this
export default {
  name: 'DtSrcoll',
  props: {
    newData: {
      // 表格数据
      type: Array,
      default: []
    },
    menuData: Array, // 表格行内容
    lineHeight: {
      // 页面需要显示的行数
      type: Number,
      default: 10
    },
    rowTime: {
      // 每一行滚动切换等待的时间（毫秒）
      type: Number,
      default: 5000
    },
    duration: {
      // 过渡时间
      type: Number,
      default: 1000
    },
    tableHeight: {
      // 行高
      type: Number,
      default: 50
    },
    isClear: {
      // 数据滚动到最后一行是否停止滚动
      type: Boolean,
      default: false
    },
    isAgain: {
      // 数据滚动到最后一行是否重新开始滚动
      type: Boolean,
      default: true
    },
    isScroll: {
      // 是否允许内容滚动
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      active: 0,
      timer: ''
    }
  },
  watch: {
    newData: {
      handler (newValue, oldValue) {
        this.newData = newValue
      },
      deep: true
    }
  },
  mounted () {
    _this = this
    this.$nextTick(() => {
      const elwrapper = document.getElementsByClassName(
        'el-table__body-wrapper'
      )[0]
      elwrapper.style.height = this.lineHeight * this.tableHeight + 'px'
      const elBody = document.getElementsByClassName('el-table__body')[0]
      const elRow = document.getElementsByClassName('el-table__row')
      for (const node of elRow) {
        node.style.height = this.tableHeight + 'px'
      }
      elBody.style.top = 0
      elBody.style.transactionDuration = this.duration + 'ms'
      if (_this.isScroll) {
        _this.timer = setInterval(function () {
          if (
            _this.active <
            parseInt(_this.newData.length) - parseInt(_this.lineHeight)
          ) {
            _this.active += 1
            elBody.style.top =
              parseInt(elBody.style.top) - parseInt(_this.tableHeight) + 'px'
          } else {
            if (this.isClear) {
              clearInterval(this.timer)
            }
            if (_this.isAgain) {
              _this.active = 0
              elBody.style.top = 0
            } else {
              clearInterval(_this.timer)
            }
          }
        }, _this.rowTime)
      }
    })
  },
  methods: {
    handleRowClick (row) {
      console.log(row)

      // 在这里处理点击行的逻辑
    }
  },
  destroyed () {
    // clearInterval(this.timer);
  }
}
</script>
<style>
.el-table__body {
  position: absolute;
  transition: all 500ms linear;
  background-color: transparent !important;
  color: #fff;
  font-size: 14px;
  overflow: hidden;
}

.el-table,
.el-table__expanded-cell {
  color: #fff;
  background-color: transparent !important;
}

.el-table th,
.el-table tr,
.el-table td {
  color: #fff;
  background-color: transparent !important;
}

.el-table--border,
.el-table--group {
  border: 0px;
}

.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  background-color: transparent !important;
  border: 0px solid transparent !important;
}

.el-table--border,
.el-table--group {
  border: 0px solid transparent !important;
}

.customer-table {
  text-align: center !important;
}

.el-table__footer-wrapper,
.el-table__header-wrapper {
  font-size: 16px;
  /* background: linear-gradient(
    to right,
    rgba(255, 0, 0, 0),
    rgba(255, 255, 255, 0.2)
  ); */
}

.el-table--scrollable-x .el-table__body-wrapper {
  overflow: hidden;
}

/* 去掉表格单元格边框 */
.customer-table th {
  border: none;
}

.customer-table td,
.customer-table th.is-leaf {
  border: none;
}

/* 表格最外边框 */
.el-table--border,
.el-table--group {
  border: none;
}

/* 头部边框 */
.customer-table thead tr th.is-leaf {
  border: 0px solid #ebeef5;
  border-right: none;
}

.customer-table thead tr th:nth-last-of-type(2) {
  border-right: 0px solid #ebeef5;
}

/* 表格最外层边框-底部边框 */
.el-table--border::after,
.el-table--group::after {
  width: 0;
}

.customer-table::before {
  width: 0;
}

.customer-table .el-table__fixed-right::before,
.el-table__fixed::before {
  width: 0;
}

/* 表格有滚动时表格头边框 */
.el-table--border th.gutter:last-of-type {
  border: 1px solid #ebeef5;
  border-left: none;
}

.pending {
  background-image: url("../assets/running.svg");
  /* background-image: url("../assets/pending.svg"); */
  width: 100%;
  height: 16px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.running {
  background-image: url("../assets/succeeded.svg");
  /* background-image: url("../assets/running.svg"); */
  width: 100%;
  height: 16px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.other {
  background-image: url("../assets/succeeded.svg");
  width: 100%;
  height: 16px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.el-table__empty-text {
  margin-top: 70px;
}
.cell {
  font-size: 14px;
}
</style>
