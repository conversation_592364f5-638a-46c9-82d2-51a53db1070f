<template>
  <div class="tableWrapper">
    <el-table
      v-if="show"
      :data="tableData"
      style="width: 100%"
      max-height="600"
      ref="multipleTable"
      tooltip-effect="dark"
      @selection-change="handleSelectionChange"
      :row-class-name="tableRowClassName"
      :row-style="getRowStyle"
      @row-click="handleRowClick"
    >
      <el-table-column
        type="selection"
        :selectable="selectableStatus"
        width="40"
        class-name="selection-column"
      >
      </el-table-column>
      <el-table-column label="ID" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.disabled"
            :content="getDisabledMessage()"
            placement="top"
            effect="dark"
          >
            <span style="color: rgba(5, 188, 253, 1)">{{ scope.row.ID }}</span>
          </el-tooltip>
          <span v-else style="color: rgba(5, 188, 253, 1)">{{ scope.row.ID }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="集群数量"
        align="center"
        show-overflow-tooltip
        min-width="85"
      >
        <template slot-scope="scope"
        ><span style="color: #fff">{{ scope.row.NCenters }}</span></template
        >
      </el-table-column>
      <el-table-column
        label="任务数"
        align="center"
        show-overflow-tooltip
        min-width="85"
      >
        <template slot-scope="scope"
        ><span style="color: #fff">{{ scope.row.NJobs }}</span></template
        >
      </el-table-column>

      <el-table-column label="仿真状态" align="center" min-width="90">
        <template slot-scope="scope">
          <div class="running" v-if="scope.row.CompletedFlag == true"></div>
          <div class="pending" v-if="scope.row.CompletedFlag != true"></div>
        </template>
      </el-table-column>
      <el-table-column
        label="仿真时长"
        align="center"
        show-overflow-tooltip
        min-width="90"
      >
        <template slot-scope="scope"
        ><span style="color: #fff">{{
            scope.row.SnapshotTime
          }}</span></template
        >
      </el-table-column>
      <el-table-column
        label="仿真策略"
        align="center"
        show-overflow-tooltip
        min-width="100"
      >
        <template slot-scope="scope"
        ><span style="color: #fff">{{ scope.row.Strategy }}</span></template
        >
      </el-table-column>
      <!-- <el-table-column label="备注" align="center" show-overflow-tooltip>
        <template slot-scope="scope"
          ><span style="color: #fff">{{ scope.row.Remark }}</span></template
        >
      </el-table-column> -->
    </el-table>

    <!-- <el-button @click="toggleSelection([tableData[1], tableData[2]])">切换第二、第三行的选中状态</el-button> -->
    <div class="block">
      <el-pagination
        small
        :current-page="pageIndex"
        :page-size="8"
        :total="total"
        layout="prev, pager, next"
        @current-change="handleCurrentChange"
        :disabled="disabled"
      />
    </div>
    <div class="stopClick" v-if="disabled"></div>
  </div>
</template>
<script>
import { getJob } from '@/api/screenService.js'
import { formatDuring } from '@/utils/index'

export default {
  props: {
    Count: {
      type: Number,
      default: 0
    },
    Total: {
      type: Number,
      default: 0
    },
    Stop: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    Stop (newValue, oldValue) {
      // console.log(newValue, oldValue);
      // console.log(newValue, oldValue)
      if (this.Stop == true) {
        this.disabled = false
      } else {
        this.disabled = true
      }
      // console.log(newValue,oldValue)
      // if (newValue == true) {
      //   clearInterval(this.timer);
      // } else {
      //   if (this.taskId !== 0) {
      //     this.count = this.Count;
      //     this.getCenter();
      //   }
      // }
      // console.log(newValue, oldValue);
    },
    Count (newValue, oldValue) {
      this.count = newValue
      if (this.count == 0 || this.count == this.Total) {
        this.disabled = false
      } else {
        this.disabled = true
      }
      // console.log(this.count,this.Total)
      // console.log(this.count,this.total);
      // console.log(this.count)
      // console.log(this.count,this.taskId)
      // if (newValue != 0) {
      //   this.getCenter();
      // }
    },
    multipleSelection: {
      handler (newValue, oldValue) {
        // 使用新的store mutation来管理多个任务
        if (this.multipleSelection.length == 0) {
          this.$store.commit('clearSelectedTasks')
          this.comparableTable = ''
        } else {
          // 将所有选中的任务存储到store中
          this.$store.commit('setSelectedTasks', this.multipleSelection)
          this.comparableTable = this.multipleSelection[0].ComparableHash

          // 输出选中任务的信息
          console.log(`已选择${this.multipleSelection.length}个任务:`,
            this.multipleSelection.map(task => `ID:${task.ID} 策略:${task.Strategy}`).join(', '))

          if (this.multipleSelection.length > 2) {
            console.log('注意：当前显示前两个任务的对比，其他任务已保存在store中')
          }
        }
      },
      deep: true
    }
  },
  created () {
    this.init()
    // this.timer = setInterval(() => {
    //   this.init();
    // }, 30000);
  },
  mounted () {
  },
  computed: {
    taskId () {
      return this.$store.state.id
    }
    // compareId() {
    //   return this.$store.state.compareId;
    // },
  },
  data () {
    return {
      tableData: null,
      allData: [], // 存储所有数据
      pageSize: 8,
      pageIndex: 1,
      total: null,
      timer: null,
      multipleSelection: [],
      selectedIds: [], // 存储所有选中行的ID
      comparableTable: '',
      show: false,
      count: 0,
      disabled: false,
      isPageChanging: false // 标记是否正在切换页码
    }
  },
  methods: {
    init () {
      console.log('Table组件init方法被调用 - 刷新任务列表')
      getJob({
        pagesize: 999999,
        pagenum: 1
      }).then((res) => {
        this.show = true
        this.allData = []
        if (res.TaskInfoToWebList == null || !res.TaskInfoToWebList) {
          res.TaskInfoToWebList = []
        }
        res.TaskInfoToWebList.forEach((item) => {
          item.SnapshotTime = formatDuring(item.SnapshotTime)
          this.allData.push(item)
        })
        this.total = this.allData.length
        this.updateTableData()
      })
    },

    updateTableData () {
      const start = (this.pageIndex - 1) * this.pageSize
      const end = start + this.pageSize
      this.tableData = this.allData.slice(start, end)

      // 等待DOM更新
      this.$nextTick(() => {
        this.judgeSelectable()
        this.updateTableSelections()
      })
    },

    handleCurrentChange (val) {
      // 标记正在切换页码，防止handleSelectionChange干扰
      this.isPageChanging = true

      // 保存当前的选中状态
      const selectedIds = [...this.selectedIds]

      // 更新页码和表格数据
      this.pageIndex = val
      const start = (this.pageIndex - 1) * this.pageSize
      const end = start + this.pageSize
      this.tableData = this.allData.slice(start, end)

      // 等待DOM更新后恢复选中状态
      this.$nextTick(() => {
        // 确保选中状态不会丢失
        this.selectedIds = selectedIds

        // 先设置可选状态
        this.judgeSelectable()

        // 然后更新表格选中
        this.updateTableSelections()

        // 延迟标记页码切换完成
        setTimeout(() => {
          this.isPageChanging = false
        }, 200)
      })
    },

    // 更新表格选中状态
    updateTableSelections () {
      const multipleTable = this.$refs.multipleTable
      if (!multipleTable) return

      // 清除所有选中
      multipleTable.clearSelection()

      // 选中应该选中的行
      this.$nextTick(() => {
        this.tableData.forEach(row => {
          if (this.selectedIds.includes(row.ID)) {
            multipleTable.toggleRowSelection(row, true)
          }
        })

        // 确保multipleSelection与selectedIds同步
        this.multipleSelection = this.allData.filter(item =>
          this.selectedIds.includes(item.ID)
        )
      })
    },

    handleSelectionChange (selection) {
      // 如果正在切换页码，不处理选择变化
      if (this.isPageChanging) return

      this.show = true

      // 获取当前页面所有行的ID
      const currentPageIds = this.tableData.map(item => item.ID)

      // 当前页面选中的ID
      const currentPageSelectedIds = selection.map(item => item.ID)

      // 保留其他页面的选中状态
      const otherPageSelectedIds = this.selectedIds.filter(id =>
        !currentPageIds.includes(id)
      )

      // 合并当前页面和其他页面的选中状态
      this.selectedIds = [...otherPageSelectedIds, ...currentPageSelectedIds]

      // 获取完整的选中对象
      this.multipleSelection = this.allData.filter(item =>
        this.selectedIds.includes(item.ID)
      )

      // 移除了最多选择2个的限制，现在可以选择多个任务

      // 更新可选状态
      this.judgeSelectable()

      if (this.multipleSelection.length !== 0) {
        clearInterval(this.timer)
      }
    },
    selectableStatus (row, rowIndex) {
      // 如果行数据中有disabled属性，则返回false禁用多选
      return !row.disabled
    },
    judgeSelectable () {
      // 当没有选择时，所有行都可选
      if (this.multipleSelection.length === 0) {
        this.tableData.forEach((item) => {
          this.$set(item, 'disabled', false)
        })
        return
      }

      // 当已选择任务时，允许选择具有相同hash的任务或已选中的任务
      if (this.multipleSelection.length >= 1) {
        const hash = this.multipleSelection[0].ComparableHash

        // 遍历当前页的所有行
        this.tableData.forEach((item) => {
          // 如果这一行已经被选中，保持它可选（可以取消选择）
          if (this.selectedIds.includes(item.ID)) {
            this.$set(item, 'disabled', false)
          }
          // 如果这一行未被选中但hash匹配，使它可选
          else if (hash === item.ComparableHash) {
            this.$set(item, 'disabled', false)
          }
          // 其他情况设为不可选
          else {
            this.$set(item, 'disabled', true)
          }
        })
      }
    },
    tableRowClassName ({ row }) {
      return row.disabled ? 'disabled-row' : ''
    },
    getRowStyle ({ row }) {
      if (row.disabled) {
        const message = this.getDisabledMessage()
        return {
          title: message
        }
      }
      return {}
    },
    getDisabledMessage () {
      // 根据选中情况返回不同提示信息
      if (this.multipleSelection.length >= 1) {
        return '只能选择具有相同可比较哈希的任务'
      }
      return '此任务不可选'
    },
    handleRowClick (row, column, event) {
      // 检查点击的是否是勾选框列
      if (column && column.className && column.className.includes('selection-column')) {
        // 如果点击的是勾选框列，不触发行点击事件
        return
      }

      // 检查点击的元素是否是勾选框或其相关元素
      if (event && event.target) {
        const target = event.target
        // 检查是否点击了勾选框、勾选框的父元素或相关元素
        if (target.type === 'checkbox' ||
            target.classList.contains('el-checkbox') ||
            target.classList.contains('el-checkbox__input') ||
            target.classList.contains('el-checkbox__inner') ||
            target.closest('.el-checkbox')) {
          // 如果点击的是勾选框相关元素，不触发行点击事件
          return
        }
      }

      // 查找父链中的c2net组件，并确保地图不会被清空
      let parent = this.$parent
      while (parent && !parent.$refs.chinaMap) {
        parent = parent.$parent
      }

      // 如果找到了父组件，并且它有chinaMap引用，先保存地图状态
      if (parent && parent.$refs.chinaMap) {
        // 防止地图消失：先保持地图组件的状态
        parent.$refs.chinaMap.stopped = false
      }

      // 传递当前任务ID给父组件
      this.$emit('row-click', row)
    }
  }
}
</script>
<style>
.pending {
  background-image: url("../assets/running.svg");
  /* background-image: url("../assets/pending.svg"); */
  width: 100%;
  height: 16px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position-x: -4px;
}

.running {
  background-image: url("../assets/succeeded.svg");
  /* background-image: url("../assets/running.svg"); */
  width: 100%;
  height: 16px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position-x: -4px;
}

.other {
  background-image: url("../assets/succeeded.svg");
  width: 100%;
  height: 16px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position-x: -4px;
}

/* 表格基础样式优化 */
/*最外层透明*/
.el-table,
.el-table__expanded-cell {
  background-color: transparent;
  font-size: 14px !important;
  font-weight: 500;
  border-radius: 6px;
  /* 添加细微的表格边框 */
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.15);
  overflow: hidden;
}

/* 表格内背景颜色 */
.el-table th,
.el-table tr,
.el-table td {
  background-color: transparent;
  transition: all 0.3s ease;
}

/* 表格表头样式优化 */
.el-table thead {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.3);
}

.el-table th.el-table__cell.is-leaf {
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding: 10px 0;
}

/* 行悬停效果优化 */
.el-table__row:hover {
  background: rgba(255, 255, 255, 0.08) !important;
}

.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background: rgba(255, 255, 255, 0.08) !important;
}

/* 表格单元格样式优化 */
.el-table td.el-table__cell {
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.95); /* 提高普通文本亮度 */
}

.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

/* 覆盖所有表格文本的样式，提高亮度 */
.el-table .cell {
  color: rgba(255, 255, 255, 0.95);
}

/* 特别提高ID列的颜色亮度 */
.el-table .cell span[style*="color: rgba(5, 188, 253, 1)"] {
  color: rgba(50, 218, 255, 1) !important; /* 更亮的蓝色 */
  text-shadow: 0 0 3px rgba(50, 218, 255, 0.3);
}

.el-table::before {
  background-color: transparent;
}

/* 表格行样式优化 */
.el-table__row {
  cursor: pointer !important;
  position: relative;
}

/* 选中行样式优化 */
.el-table__row.current-row td {
  background-color: rgba(64, 158, 255, 0.15) !important;
  border-color: rgba(64, 158, 255, 0.3);
}

/* 选中行效果 */
.el-table__row.el-table__row--striped td.el-table__cell {
  background: rgba(255, 255, 255, 0.04);
}

/* 分页控件样式优化 */
.block {
  float: right;
  margin-top: 15px;
  margin-right: 5px;
}

.el-pagination {
  background: transparent !important;
  border-radius: 4px;
  padding: 5px;
  display: flex;
  align-items: center;
}

.el-pagination button {
  background-color: transparent !important;
  color: rgba(255, 255, 255, 0.95); /* 提高按钮文字亮度 */
  transition: all 0.3s ease;
  border: none;
}

.el-pagination button:hover,
.el-pagination button:focus {
  color: #58aeff; /* 更亮的高亮色 */
}

.el-pager {
  background-color: transparent !important;
  display: flex;
  align-items: center;
}

.el-pager li {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.95); /* 提高分页数字亮度 */
  font-weight: 500;
  min-width: 28px;
  height: 28px;
  line-height: 28px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.el-pager li.active {
  background-color: #58aeff !important; /* 更亮的选中色 */
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.5);
}

.el-pager li:hover:not(.active) {
  color: #58aeff; /* 更亮的高亮色 */
}

.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  color: rgba(255, 255, 255, 0.95); /* 提高图标亮度 */
}

.el-table .el-table__cell.gutter {
  background: transparent !important;
}

.stopClick {
  position: relative;
  width: 600px;
  height: 500px;
  background: transparent;
  top: -440px;
  z-index: 100;
}

/* 勾选框列样式优化 */
.el-table .selection-column {
  padding: 0 !important;
}

.el-table .selection-column .cell {
  padding: 8px 12px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 勾选框样式优化 */
.el-table .el-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 32px;
}

.el-table .el-checkbox__input {
  line-height: 1;
}

.el-table .el-checkbox__inner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  background-color: transparent;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.el-table .el-checkbox__inner:hover {
  border-color: rgba(88, 174, 255, 0.8);
  background-color: rgba(88, 174, 255, 0.1);
}

.el-table .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #58aeff;
  border-color: #58aeff;
}

.el-table .el-checkbox__input.is-checked .el-checkbox__inner::after {
  border-color: #ffffff;
  border-width: 2px;
}

/* 禁用状态的勾选框 */
.el-table .disabled-row .el-checkbox__inner {
  border-color: rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.05);
  cursor: not-allowed;
}

.el-table .disabled-row .el-checkbox__input.is-disabled .el-checkbox__inner {
  border-color: rgba(255, 255, 255, 0.2);
  background-color: rgba(255, 255, 255, 0.03);
}

.tableWrapper {
  height: 480px;
  overflow: hidden;
}

/* 不可选行的样式优化 */
.el-table .disabled-row {
  opacity: 0.7; /* 提高不可选行的可见度 */
  position: relative;
}

.el-table .disabled-row::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

/* 表格checkbox样式优化 */
.el-table .el-checkbox__inner {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
}

.el-table .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #58aeff; /* 更亮的选中色 */
  border-color: #58aeff;
}

/* 表格单元格内容的对齐和间距优化 */
.el-table .cell {
  padding: 0 10px;
  line-height: 1.5;
}

/* 任务状态图标容器优化 */
.status-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 表格加载状态优化 */
.el-table .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
}

/* 表格空数据状态优化 */
.el-table__empty-block {
  background: rgba(255, 255, 255, 0.03);
  min-height: 100px;
}

.el-table__empty-text {
  color: rgba(255, 255, 255, 0.7); /* 提高空数据提示亮度 */
}

/* 优化tooltip显示样式 */
.el-tooltip__popper.is-dark {
  background-color: rgba(0, 10, 26, 0.9) !important;
  color: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(64, 158, 255, 0.2) !important;
}
</style>
