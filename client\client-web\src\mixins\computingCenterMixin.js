/**
 * 计算中心混入 - 封装计算中心相关操作逻辑
 */
import { c2netComputingCenterList } from '@/utils/centerDefault'
import { getCenter } from '@/api/screenService'
import { getDefaultCenterForm } from '@/utils/form'

// 全局状态管理，避免重复请求
let globalLocationOptions = []
let globalLocationOptionsPromise = null

export default {
  data () {
    return {
      centerDialogVisible: false,
      editIndex: -1,
      locationOptions: [], // 存储API获取的计算节点数据
      computingCenters: [], // 存储添加的计算中心
      centerForm: getDefaultCenterForm(),
      // 加速卡算力对照表 (Pops/@FP16/卡)
      // 根据centerDefault.js中的配置数据计算得出:
      // 例如ASCEND910: pOPS=100, nNodes=50, nGraphicsCards=8
      // 单卡算力 = pOPS / (nNodes * nGraphicsCards) = 100 / (50 * 8) = 0.25
      // 但为了计算方便，我们记录的是单节点的算力值 (单节点有8张卡)
      cardPerformance: {
        ASCEND910: 0.25, // 单节点算力: 0.25 * 8 = 2 Pops
        A100: 0.25, // 单节点算力: 0.25 * 8 = 2 Pops，部分集群为100/50/8=0.25
        V100: 0.2, // 单节点算力: 0.2 * 8 = 1.6 Pops，从32/20/8=0.2计算得出
        'ENFLAME-T20': 0.275 // 单节点算力: 0.275 * 8 = 2.2 Pops，根据官方数据估算
      }
    }
  },
  methods: {
    /**
     * 获取计算节点数据
     */
    fetchLocationOptions () {
      // 如果全局已有数据，直接使用
      if (globalLocationOptions.length > 0) {
        this.locationOptions = globalLocationOptions
        return Promise.resolve(globalLocationOptions)
      }

      // 如果正在请求中，返回现有的Promise
      if (globalLocationOptionsPromise) {
        return globalLocationOptionsPromise.then(() => {
          this.locationOptions = globalLocationOptions
          return globalLocationOptions
        })
      }

      // 创建新的请求Promise
      globalLocationOptionsPromise = getCenter()
        .then(response => {
          globalLocationOptions = response.LocationInfoToWebList
          this.locationOptions = globalLocationOptions
          return globalLocationOptions
        })
        .catch(error => {
          console.error('获取计算节点数据失败:', error)
          this.$message.error('获取计算节点数据失败，请稍后重试')
          throw error
        })
        .finally(() => {
          globalLocationOptionsPromise = null
        })

      return globalLocationOptionsPromise
    },

    /**
     * 显示添加计算中心对话框
     */
    showAddComputingCenterDialog () {
      this.editIndex = -1
      this.centerForm = getDefaultCenterForm()
      this.centerDialogVisible = true

      // 确保获取计算节点数据
      this.fetchLocationOptions()
    },

    /**
     * 编辑计算中心
     * @param {Object} row - 行数据
     * @param {Number} index - 行索引
     */
    editComputingCenter (row, index) {
      this.editIndex = index
      this.centerForm = { ...row }
      this.centerDialogVisible = true
    },

    /**
     * 删除计算中心
     * @param {Number} index - 索引
     */
    removeComputingCenter (index) {
      this.showConfirm({
        message: '确定要删除该计算中心吗?'
      }).then(() => {
        this.computingCenters.splice(index, 1)
        this.showSuccess('删除成功!')
      }).catch(() => {
        // 用户取消，不做任何处理
      })
    },

    /**
     * 提交计算中心表单
     */
    submitCenterForm () {
      this.$refs.centerForm.validate(valid => {
        if (valid) {
          const centerData = { ...this.centerForm }

          // 检查是否重复添加相同的计算中心
          const existingIndex = this.computingCenters.findIndex(center => center.name === centerData.name)

          if (this.editIndex === -1) {
            // 添加新计算中心时检查重复
            if (existingIndex !== -1) {
              this.$message.warning(`计算中心"${centerData.name}"已存在，请勿重复添加`)
              return
            }
            this.computingCenters.push(centerData)
            this.$message.success('计算中心添加成功')
          } else {
            // 编辑现有计算中心时，排除当前编辑的项检查重复
            if (existingIndex !== -1 && existingIndex !== this.editIndex) {
              this.$message.warning(`计算中心"${centerData.name}"已存在，请选择其他名称`)
              return
            }
            this.computingCenters.splice(this.editIndex, 1, centerData)
            this.$message.success('计算中心编辑成功')
          }
          this.centerDialogVisible = false
        }
      })
    },

    /**
     * 监听计算中心名称变化，自动填充默认值
     * @param {String} newName - 新名称
     */
    handleCenterNameChange (newName) {
      if (newName) {
        // 在默认值列表中查找匹配的计算中心
        const defaultCenter = c2netComputingCenterList.find(
          item => item.c2netComputingCenterSpecification.name === newName
        )

        // 如果找到匹配的计算中心，填充默认值
        if (defaultCenter) {
          const spec = defaultCenter.c2netComputingCenterSpecification
          const node = spec.nodeSpecification

          this.centerForm = {
            name: newName,
            pOPS: spec.pOPS,
            nNodes: spec.nNodes,
            graphicsCardType: node.graphicsCardType,
            nGraphicsCards: node.nGraphicsCards,
            nGBMemoriesPerGraphicsCard: node.nGBMemoriesPerGraphicsCard,
            nCpus: node.nCpus,
            nGBMemories: node.nGBMemories
          }
        }
      }
    },

    /**
     * 计算算力规模
     * 根据节点数、加速卡类型和每节点加速卡数计算总算力
     * 计算公式: 节点数 * 每节点加速卡数 * 单卡算力
     * 例如: 50个节点，每节点8卡ASCEND910，总算力 = 50 * 8 * 0.25 = 100 Pops
     */
    calculatePOPS () {
      const { nNodes, graphicsCardType, nGraphicsCards } = this.centerForm

      // 所有参数都有值时才进行计算
      if (nNodes && graphicsCardType && nGraphicsCards) {
        // 获取当前加速卡的单卡算力
        const cardPower = this.cardPerformance[graphicsCardType] || 0.25

        // 计算总算力: 节点数 * 每节点卡数 * 单卡算力
        const totalPOPS = nNodes * nGraphicsCards * cardPower

        // 更新表单中的算力值，四舍五入到整数
        this.centerForm.pOPS = Math.round(totalPOPS)
      }
    }
  },
  watch: {
    // 监听计算中心名称变化，自动填充默认值
    'centerForm.name': {
      handler (newName) {
        this.handleCenterNameChange(newName)
      },
      immediate: false
    },

    // 监听节点数变化，自动计算算力
    'centerForm.nNodes': {
      handler () {
        this.calculatePOPS()
      }
    },

    // 监听加速卡类型变化，自动计算算力
    'centerForm.graphicsCardType': {
      handler () {
        this.calculatePOPS()
      }
    },

    // 监听每节点加速卡数变化，自动计算算力
    'centerForm.nGraphicsCards': {
      handler () {
        this.calculatePOPS()
      }
    }
  }
}
