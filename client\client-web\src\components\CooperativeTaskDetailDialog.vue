<template>
  <div>
    <el-dialog
      :before-close="handleClose"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      custom-class="cooperative-task-dialog"
      title="协同任务详情"
      top="3vh"
      width="900px"
    >
      <div class="cooperative-task-container">
        <!-- 标签页容器 -->
        <el-tabs v-model="activeTab" class="task-detail-tabs" type="card">

          <!-- 编辑配置标签页 -->
          <el-tab-pane label="编辑配置" name="config">
            <div v-loading="configLoading" class="config-panel">
              <el-form ref="taskForm" :model="taskForm" class="form-container" label-suffix=":" label-width="160px">

                <!-- 计算中心管理组件 -->
                <computing-center-manager
                  v-model="computingCenters"
                  @change="handleComputingCentersChange"
                />

                <!-- 网络配置组件 -->
                <network-config
                  ref="networkConfig"
                  v-model="networkConfig"
                  @change="handleNetworkConfigChange"
                />

                <!-- 任务流编辑组件 -->
                <task-flow-editor
                  ref="taskFlowEditor"
                  v-model="taskFlowConfig"
                  :computing-centers="computingCenters"
                  :initial-comm-types="initialCommTypes"
                  :initial-tasks="initialTasks"
                  :node-json-data="nodeJsonData"
                  @change="handleTaskFlowChange"
                />

              </el-form>
            </div>
          </el-tab-pane>

          <!-- 任务日志标签页 -->
          <el-tab-pane label="任务日志" name="log">
            <div v-loading="logLoading" class="log-panel">
              <div class="log-header">
                <span class="section-title">执行日志</span>
                <el-button size="small" type="primary" @click="refreshLogs">刷新日志</el-button>
              </div>

              <div class="log-content-wrapper">
                <div v-if="logContent" class="log-content">
                  <div v-for="(line, index) in paginatedLogs" :key="index" class="log-line">
                    <span class="log-number">{{ logConfig.currentPage * logConfig.pageSize - logConfig.pageSize + index + 1 }}</span>
                    <span class="log-text">{{ line }}</span>
                  </div>
                </div>
                <div v-else class="empty-log">
                  <i class="el-icon-document"></i>
                  <p>暂无日志内容</p>
                </div>
              </div>

              <!-- 分页器 -->
              <el-pagination
                v-if="logLines.length > logConfig.pageSize"
                :current-page="logConfig.currentPage"
                :page-size="logConfig.pageSize"
                :total="logLines.length"
                background
                class="log-pagination"
                layout="total, prev, pager, next, jumper"
                @current-change="handlePageChange"
              />
            </div>
          </el-tab-pane>

          <!-- 任务状态标签页 -->
          <el-tab-pane label="任务状态" name="status">
            <div class="status-container">
              <div class="section-header">
                <span class="section-title">任务基本信息</span>
              </div>
              <div class="task-info-grid">
                <!-- 第一行 -->
                <div class="info-row">
                  <div class="info-label">任务ID</div>
                  <div class="info-value">{{ taskInfo.ID || '--' }}</div>
                  <div class="info-label">状态</div>
                  <div class="info-value status-cell">
                    <div :class="taskInfo.CompletedFlag ? 'status-completed' : 'status-running'">
                      {{ taskInfo.CompletedFlag ? '已完成' : '运行中' }}
                    </div>
                  </div>
                </div>
                <!-- 第二行 -->
                <div class="info-row">
                  <div class="info-label">调度策略</div>
                  <div class="info-value">{{ taskInfo.Strategy || '--' }}</div>
                  <div class="info-label">集群数量</div>
                  <div class="info-value">{{ taskInfo.NCenters || '--' }}</div>
                </div>
                <!-- 第三行 -->
                <div class="info-row">
                  <div class="info-label">任务数</div>
                  <div class="info-value">{{ taskInfo.NJobs || '--' }}</div>
                  <div class="info-label">仿真时长</div>
                  <div class="info-value">{{ taskInfo.SnapshotTime || '--' }}</div>
                </div>
                <!-- 第四行 -->
                <div class="info-row">
                  <div class="info-label">算力规模</div>
                  <div class="info-value">{{ taskInfo.NPops ? taskInfo.NPops + ' Pops' : '--' }}</div>
                  <div class="info-label">备注</div>
                  <div class="info-value">{{ taskInfo.Remark || '--' }}</div>
                </div>
                <!-- 第五行 -->
                <div class="info-row">
                  <div class="info-label">创建时间</div>
                  <div class="info-value">{{ formatTime(taskInfo.CreatedAt) || '--' }}</div>
                  <div class="info-label">更新时间</div>
                  <div class="info-value">{{ formatTime(taskInfo.UpdatedAt) || '--' }}</div>
                </div>
                <!-- 第六行 -->
                <div class="info-row">
                  <div class="info-label">配置文件</div>
                  <div class="info-value config-file" colspan="3">{{ taskInfo.YamlFilename || '--' }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>

        </el-tabs>
      </div>

      <span slot="footer" class="dialog-footer">
        <template v-if="activeTab === 'config'">
          <el-button class="preview-btn" type="info" @click="previewYaml">预览 YAML</el-button>
          <el-button class="cancel-btn" @click="handleClose">取 消</el-button>
          <el-button class="submit-btn" type="primary" @click="resubmitTask">重新提交</el-button>
        </template>
        <template v-else>
          <el-button class="cancel-btn" @click="handleClose">关 闭</el-button>
        </template>
      </span>
    </el-dialog>

    <!-- YAML预览对话框 -->
    <el-dialog
      :visible.sync="yamlPreviewVisible"
      :close-on-click-modal="false"
      append-to-body
      custom-class="task-dialog yaml-preview-dialog"
      title="YAML 预览"
      width="600px"
    >
      <div v-if="yamlFilename" class="yaml-info">
        <span>文件名: {{ yamlFilename }}</span>
      </div>
      <div v-loading="yamlLoading" class="yaml-content-container">
        <pre v-if="yamlContent" class="yaml-content">{{ yamlContent }}</pre>
        <div v-else class="yaml-empty">
          <span>暂无内容，请点击"生成YAML"按钮生成预览</span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="yamlPreviewVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { generateYaml, getTaskLog, loadTaskYaml } from '@/api/yamlService'
import dialogMixin from '@/mixins/dialogMixin'
import computingCenterMixin from '@/mixins/computingCenterMixin'
import yamlPreviewMixin from '@/mixins/yamlPreviewMixin'
import ComputingCenterManager from '@/components/common/ComputingCenterManager.vue'
import TaskFlowEditor from '@/components/common/TaskFlowEditor.vue'
import NetworkConfig from '@/components/common/NetworkConfig.vue'
import { handleApiRequest } from '@/utils/apiHelper'

export default {
  name: 'CooperativeTaskDetailDialog',
  components: {
    ComputingCenterManager,
    TaskFlowEditor,
    NetworkConfig
  },
  mixins: [
    dialogMixin,
    computingCenterMixin,
    yamlPreviewMixin
  ],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    taskData: {
      type: Object,
      default: null
    },
    // 保留taskId作为兼容性支持
    taskId: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      activeTab: 'config', // 当前活动标签页
      configLoading: false,
      logLoading: false,

      // 任务信息
      taskInfo: {},

      // 表单数据
      taskForm: {},

      // 协同任务配置
      computingCenters: [], // 计算中心数据
      taskFlowConfig: {
        nodes: [],
        connections: []
      }, // 任务流配置数据
      networkConfig: {
        construct: false,
        accelerator: false,
        topologyTpye: 'topology01'
      },
      scheduleConfig: {
        scheduleType: ''
      },

      // 任务流初始数据（传统方式解析）
      initialCommTypes: [], // 初始通信类型数据
      initialTasks: [], // 初始任务数据

      // 节点JSON数据用于恢复拓扑图
      nodeJsonData: null,

      // 日志相关
      logContent: '',
      logConfig: {
        currentPage: 1,
        pageSize: 50
      }
    }
  },
  computed: {
    // 日志行数组
    logLines () {
      return this.logContent ? this.logContent.split('\n').filter(line => line.trim() !== '') : []
    },

    // 分页后的日志
    paginatedLogs () {
      const start = (this.logConfig.currentPage - 1) * this.logConfig.pageSize
      const end = start + this.logConfig.pageSize
      return this.logLines.slice(start, end)
    }
  },
  watch: {
    visible (val) {
      console.log('协同任务详情弹窗visible变化:', val, 'taskData:', !!this.taskData, 'taskId:', this.taskId)
      this.dialogVisible = val
      if (val) {
        if (this.taskData) {
          // 优先使用传入的任务数据
          this.loadTaskDataFromProps()
        } else if (this.taskId) {
          // 兼容模式：从API获取数据
          this.loadTaskData()
        }
      } else if (!val) {
        // 弹窗关闭时清理状态
      }
    },
    taskData (val) {
      console.log('协同任务详情taskData变化:', !!val)
      if (this.dialogVisible && val) {
        this.loadTaskDataFromProps()
      }
    },
    taskId (val) {
      console.log('协同任务详情taskId变化:', val)
      if (this.dialogVisible && val && !this.taskData) {
        // 只有在没有taskData时才使用taskId
        this.loadTaskData()
      }
    },
    dialogVisible (val) {
      console.log('协同任务详情dialogVisible变化:', val)
      if (!val) {
        this.$emit('update:visible', false)
      }
    },
    activeTab (val) {
      // 切换到日志标签页时加载日志
      if (val === 'log') {
        this.refreshLogs()
      }
    }
  },
  methods: {
    // 新增：从props加载任务数据
    loadTaskDataFromProps () {
      if (!this.taskData) {
        console.error('没有taskData，无法加载协同任务数据')
        return
      }

      console.log('从props加载协同任务数据:', this.taskData)
      this.configLoading = true

      try {
        // 确保获取计算节点数据
        this.fetchLocationOptions()

        // 设置任务信息
        this.taskInfo = this.taskData.taskInfo || {}

        // 处理节点JSON数据恢复（完全使用nodeJson数据）
        if (this.taskData.taskInfo && this.taskData.taskInfo.Params && this.taskData.taskInfo.Params.nodeJson) {
          this.nodeJsonData = this.taskData.taskInfo.Params.nodeJson
          console.log('发现节点JSON数据，将完全使用nodeJson恢复:', this.nodeJsonData)
          console.log('nodeJsonData结构:', JSON.stringify(this.nodeJsonData, null, 2))

          // 不再设置initialCommTypes和initialTasks，让TaskFlowEditor直接从nodeJson恢复
          this.initialCommTypes = []
          this.initialTasks = []
        } else {
          this.nodeJsonData = null
          this.initialCommTypes = []
          this.initialTasks = []
          console.log('未发现节点JSON数据')
        }

        // 处理配置数据（使用传统方式）
        const config = this.taskData.yamlData || {}
        console.log('协同任务配置数据完整结构:', JSON.stringify(config, null, 2))

        // 处理计算中心列表
        this.computingCenters = []
        if (config.c2netComputingCenterList && config.c2netComputingCenterList.length > 0) {
          this.computingCenters = config.c2netComputingCenterList.map(center => {
            const spec = center.c2netComputingCenterSpecification
            const nodeSpec = spec.nodeSpecification

            return {
              name: spec.name,
              pOPS: spec.pOPS,
              nNodes: spec.nNodes,
              graphicsCardType: nodeSpec.graphicsCardType,
              nGraphicsCards: nodeSpec.nGraphicsCards,
              nGBMemoriesPerGraphicsCard: nodeSpec.nGBMemoriesPerGraphicsCard,
              nCpus: nodeSpec.nCpus,
              nGBMemories: nodeSpec.nGBMemories
            }
          })
        }

        // 处理调度配置
        if (config.scheduleConfig) {
          const scheduleConfig = config.scheduleConfig
          if (scheduleConfig.mandatoryComputingCenterSchedule) {
            this.scheduleConfig.scheduleType = '本地调度'
          } else if (scheduleConfig.heteroComputingCenterSchedule) {
            this.scheduleConfig.scheduleType = '异构调度'
          } else {
            this.scheduleConfig.scheduleType = '同构调度'
          }
        }

        // 处理网络配置
        if (config.networkConfig) {
          this.networkConfig = {
            construct: config.networkConfig.construct,
            accelerator: config.networkConfig.accelerator,
            topologyTpye: config.networkConfig.topologyTpye
          }
          console.log('从taskData恢复网络配置:', this.networkConfig)
        } else {
          console.log('taskData中没有网络配置，使用默认值:', this.networkConfig)
        }

        this.configLoading = false
      } catch (error) {
        console.error('处理props中的任务数据时出错:', error)
        this.configLoading = false
      }
    },

    // 加载协同任务详情和配置（兼容模式）
    loadTaskData () {
      if (!this.taskId) {
        console.error('没有taskId，无法加载协同任务数据')
        return
      }

      console.log('开始加载协同任务数据，taskId:', this.taskId)
      this.configLoading = true

      // 确保获取计算节点数据
      this.fetchLocationOptions()

      handleApiRequest(() => loadTaskYaml(this.taskId), {
        errorMessage: '加载协同任务配置失败，请稍后重试',
        onSuccess: (response) => {
          console.log('获取到协同任务数据:', response)
          // 设置任务信息
          this.taskInfo = response.task || {}

          // 处理节点JSON数据恢复（完全使用nodeJson数据）
          if (response.task && response.task.Params && response.task.Params.nodeJson) {
            this.nodeJsonData = response.task.Params.nodeJson
            console.log('发现节点JSON数据，将完全使用nodeJson恢复:', this.nodeJsonData)
            console.log('nodeJsonData结构:', JSON.stringify(this.nodeJsonData, null, 2))

            // 不再设置initialCommTypes和initialTasks，让TaskFlowEditor直接从nodeJson恢复
            this.initialCommTypes = []
            this.initialTasks = []
          } else {
            this.nodeJsonData = null
            this.initialCommTypes = []
            this.initialTasks = []
            console.log('未发现节点JSON数据')
          }

          // 处理配置数据
          const config = response.yaml_data || {}
          console.log('协同任务配置数据完整结构:', JSON.stringify(config, null, 2))

          // 处理计算中心列表
          this.computingCenters = []
          if (config.c2netComputingCenterList && config.c2netComputingCenterList.length > 0) {
            this.computingCenters = config.c2netComputingCenterList.map(center => {
              const spec = center.c2netComputingCenterSpecification
              const nodeSpec = spec.nodeSpecification

              return {
                name: spec.name,
                pOPS: spec.pOPS,
                nNodes: spec.nNodes,
                graphicsCardType: nodeSpec.graphicsCardType,
                nGraphicsCards: nodeSpec.nGraphicsCards,
                nGBMemoriesPerGraphicsCard: nodeSpec.nGBMemoriesPerGraphicsCard,
                nCpus: nodeSpec.nCpus,
                nGBMemories: nodeSpec.nGBMemories
              }
            })
          }

          // 注意：通信类型和任务数据现在从nodeJson获取，不再从yaml解析

          // 处理调度配置
          if (config.scheduleConfig) {
            const scheduleConfig = config.scheduleConfig
            if (scheduleConfig.mandatoryComputingCenterSchedule) {
              this.scheduleConfig.scheduleType = '本地调度'
            } else if (scheduleConfig.heteroComputingCenterSchedule) {
              this.scheduleConfig.scheduleType = '异构调度'
            } else {
              this.scheduleConfig.scheduleType = '同构调度'
            }
          }

          // 处理网络配置
          if (config.networkConfig) {
            this.networkConfig = {
              construct: config.networkConfig.construct,
              accelerator: config.networkConfig.accelerator,
              topologyTpye: config.networkConfig.topologyTpye
            }
            console.log('从yaml_data恢复网络配置:', this.networkConfig)
          } else {
            console.log('yaml_data中没有网络配置，使用默认值:', this.networkConfig)
          }
        },
        onComplete: () => {
          this.configLoading = false
        }
      })
    },

    // 获取任务日志
    refreshLogs () {
      const currentTaskId = this.taskData ? this.taskData.taskId : this.taskId
      if (!currentTaskId) return

      this.logLoading = true

      getTaskLog(currentTaskId)
        .then(logText => {
          this.logContent = logText || ''
          // 重置到第一页
          this.logConfig.currentPage = 1
        })
        .catch(error => {
          // 获取协同任务日志失败: error
          this.$message.error('获取协同任务日志失败，请稍后重试')
        })
        .finally(() => {
          this.logLoading = false
        })
    },

    // 重新提交协同任务
    resubmitTask () {
      // 验证计算中心
      if (this.computingCenters.length === 0) {
        this.$message.error('请至少添加一个计算中心')
        return
      }

      // 验证任务流配置
      if (!this.$refs.taskFlowEditor || !this.$refs.taskFlowEditor.validateTaskFlow()) {
        this.$message.error('请完善任务流配置')
        return
      }

      const apiRequestData = this.prepareCooperativeTaskData()
      if (!apiRequestData) return

      handleApiRequest(() => generateYaml(apiRequestData), {
        loadingText: '正在重新提交协同任务...',
        successMessage: '协同任务重新提交成功！',
        errorMessage: '协同任务重新提交失败，请检查参数后重试',
        onSuccess: (response) => {
          // 延迟1秒后触发submit-success事件并关闭对话框
          setTimeout(() => {
            // 触发submit-success事件，response: response
            this.$emit('submit-success', response)
            this.handleClose() // 关闭对话框
          }, 1000)
        }
      })
    },

    // 准备协同任务数据
    prepareCooperativeTaskData () {
      // 从TaskFlowEditor获取任务流数据（已包含nodeJson）
      const taskFlowData = this.$refs.taskFlowEditor ? this.$refs.taskFlowEditor.prepareTaskFlowData() : null
      if (!taskFlowData) {
        this.$message.error('任务流配置不完整')
        return null
      }

      // 使用当前的计算中心数据替换taskFlowData中的计算中心列表
      if (this.computingCenters && this.computingCenters.length > 0) {
        const c2netComputingCenterList = this.computingCenters.map(center => ({
          c2netComputingCenterSpecification: {
            nodeSpecification: {
              nGBMemoriesPerGraphicsCard: center.nGBMemoriesPerGraphicsCard,
              nCpus: center.nCpus,
              nGBMemories: center.nGBMemories,
              nGraphicsCards: center.nGraphicsCards,
              graphicsCardType: center.graphicsCardType
            },
            nNodes: center.nNodes,
            pOPS: center.pOPS,
            name: center.name
          },
          nCopiedC2netComputingCenters: 1
        }))

        taskFlowData.c2netComputingCenterList = c2netComputingCenterList
      }

      // 添加网络配置
      taskFlowData.networkConfig = this.networkConfig
      // 提交网络配置数据: this.networkConfig

      // 添加任务ID用于重新提交
      const currentTaskId = this.taskData ? this.taskData.taskId : this.taskId
      taskFlowData.backendConfig = {
        taskId: currentTaskId ? parseInt(currentTaskId) : 0
      }

      // 确保params对象存在
      if (!taskFlowData.params) {
        taskFlowData.params = {}
      }

      // 协同任务详情页面数据包含nodeJson: !!(taskFlowData.params && taskFlowData.params.nodeJson)
      // 完整提交数据结构: taskFlowData
      return taskFlowData
    },

    // 重写 yamlPreviewMixin 中的 prepareTaskData 方法
    prepareTaskData () {
      return this.prepareCooperativeTaskData()
    },

    // 处理计算中心变化
    handleComputingCentersChange (centers) {
      this.computingCenters = centers
    },

    // 处理任务流配置变化
    handleTaskFlowChange (config) {
      this.taskFlowConfig = config
      // 协同任务详情接收到TaskFlow配置: config
    },

    // 处理网络配置变化
    handleNetworkConfigChange (config) {
      // NetworkConfig组件配置变化: config
      this.networkConfig = { ...config }
      // 更新后的networkConfig: this.networkConfig
    },

    // 处理关闭
    handleClose () {
      // 清理TaskFlowEditor画布内容，避免下次打开时闪现旧数据
      if (this.$refs.taskFlowEditor && this.$refs.taskFlowEditor.graph) {
        this.$refs.taskFlowEditor.graph.clearCells()
      }

      this.dialogVisible = false
      this.$emit('update:visible', false)
      this.$emit('close')

      // 重置数据
      this.activeTab = 'config'
      this.taskInfo = {}
      this.computingCenters = []
      this.taskFlowConfig = { nodes: [], connections: [] }
      this.networkConfig = {
        construct: false,
        accelerator: false,
        topologyTpye: 'topology01'
      }
      this.scheduleConfig = { scheduleType: '' }
      this.logContent = ''
      this.initialCommTypes = []
      this.initialTasks = []
      this.nodeJsonData = null
    },

    // 处理页码变化
    handlePageChange (page) {
      this.logConfig.currentPage = page
    },

    // 格式化时间函数
    formatTime (time) {
      if (!time) return '未知'
      return new Date(time).toLocaleString()
    }

  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/dialog.scss';

/* 特定于CooperativeTaskDetailDialog的样式 */
.cooperative-task-container {
  .form-container {
    padding: 0 10px;
  }
}

/* 信息文本样式 */
.info-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

/* 日志面板样式 */
.log-panel {
  min-height: 400px;

  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(18, 137, 221, 0.5);
    padding-bottom: 8px;
  }

  .log-content-wrapper {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(18, 137, 221, 0.3);
    border-radius: 4px;
    padding: 10px;
    max-height: 350px;
    overflow-y: auto;
    margin-bottom: 15px;
  }

  .log-content {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
  }

  .log-line {
    display: flex;
    margin-bottom: 2px;

    .log-number {
      color: rgba(255, 255, 255, 0.5);
      width: 50px;
      text-align: right;
      margin-right: 10px;
      user-select: none;
      flex-shrink: 0;
    }

    .log-text {
      color: rgba(255, 255, 255, 0.9);
      word-break: break-all;
      white-space: pre-wrap;
    }
  }

  .empty-log {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    padding: 50px 0;

    i {
      font-size: 48px;
      margin-bottom: 10px;
      display: block;
    }
  }

  .log-pagination {
    display: flex;
    justify-content: center;

    :deep(.el-pagination) {
      background-color: transparent;
    }

    :deep(.el-pagination button),
    :deep(.el-pagination .el-pager li) {
      background-color: rgba(18, 137, 221, 0.1);
      color: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(18, 137, 221, 0.3);
    }

    :deep(.el-pagination button:hover),
    :deep(.el-pagination .el-pager li:hover) {
      background-color: rgba(18, 137, 221, 0.2);
      color: #32c5ff;
    }

    :deep(.el-pagination .el-pager li.active) {
      background-color: #32c5ff;
      color: #fff;
    }
  }
}
</style>
