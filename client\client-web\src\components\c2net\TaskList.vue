<template>
  <div class="task-list">
    <div class="showMt">
      <el-row class="linear-gradient">
        <el-col :span="19">
          <el-row align="middle" type="flex">
            <el-col :span="3">
              <div class="arrow"></div>
            </el-col>
            <el-col :span="16">
              <div class="title1">仿真任务列表</div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="5" style="text-align: right; padding-right: 15px;">
          <el-button
            size="small"
            type="primary"
            @click="addTask"
          >新增任务
          </el-button>
        </el-col>
      </el-row>
      <Table
        ref="table"
        :Count="count"
        :Stop="stopped"
        :Total="total"
        class="index"
        @row-click="handleRowClick"
      ></Table>
    </div>
  </div>
</template>

<script>
import Table from '@/components/Table'

/**
 * 任务列表组件 - 显示任务列表和新增任务按钮
 */
export default {
  name: 'TaskList',
  components: {
    Table
  },
  props: {
    /**
     * 当前计数
     */
    count: {
      type: Number,
      default: 0
    },
    /**
     * 任务总数
     */
    total: {
      type: Number,
      default: 0
    },
    /**
     * 是否停止
     */
    stopped: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    /**
     * 添加任务
     */
    addTask () {
      this.$emit('add-task')
    },

    /**
     * 处理行点击
     */
    handleRowClick (row) {
      this.$emit('row-click', row)
    },

    /**
     * 初始化表格
     */
    initTable () {
      this.$refs.table && this.$refs.table.init()
    }
  }
}
</script>

<style scoped>
.showMt {
  margin-top: 0px !important;
  width: 570px;
}

.arrow {
  background-size: 100% 100%;
  background-image: url("~@/static/screen1/arrow2.png");
  width: 50px;
  height: 35px;
  position: relative;
  top: -5px;
  background-repeat: no-repeat;
}

.linear-gradient {
  background: linear-gradient(
    to right,
    rgba(255, 0, 0, 0),
    rgba(255, 255, 255, 0.2)
  );
  margin: 12px 0 18px 0;
  box-sizing: content-box;
  background-clip: content-box;
}

.title1 {
  color: rgb(255, 255, 255);
  font-size: 20px;
  text-align: left;
  font-family: SourceHanSansSC-medium;
}

.index {
  z-index: 120;
}
</style>
