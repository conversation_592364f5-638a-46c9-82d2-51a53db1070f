<template>
  <div class="map-control-panel">
    <div class="progress">
      <div style="width: 600px; margin: 0 auto">
        <div style="display: inline-block; min-width: 150px; margin-right: 10px;">
          <el-slider
            v-if="showPercentage && intervalChangeEnabled"
            v-model="percentage"
            :disabled="disabled"
            :format-tooltip="formatTooltip"
            class="progressStyle"
            @change="changeProgress"
          ></el-slider>
        </div>
        <div
          v-if="intervalChangeEnabled"
          style="width: 80px; display: inline-block"
        >
          <el-button
            :disabled="disabled"
            size="small"
            type="primary"
            class="control-button"
            @click="start"
          >开始
          </el-button>
        </div>
        <div
          v-if="isPlaying && showButton"
          style="width: 80px; display: inline-block"
        >
          <el-button
            :disabled="disabled"
            size="small"
            type="warning"
            class="control-button"
            @click="stop"
          >暂停
          </el-button>
        </div>
        <div
          v-if="!isPlaying && showButton"
          style="width: 80px; display: inline-block"
        >
          <el-button
            :disabled="disabled"
            size="small"
            type="success"
            class="control-button"
            @click="continuePlay"
          >继续
          </el-button>
        </div>
        <div style="display: inline-block">
          <el-select
            v-if="intervalChangeEnabled"
            v-model="intervalValue"
            :disabled="disabled"
            class="interval"
            placeholder="请选择仿真时间间隔"
            style="width: 150px; display: inline-block; margin-left: 10px;"
            @change="changeInterval"
          >
            <el-option
              v-for="item in intervalOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 地图控制面板 - 提供进度条、播放控制和时间间隔选择
 */
export default {
  name: 'MapControlPanel',
  props: {
    /**
     * 当前计数
     */
    count: {
      type: Number,
      default: 0
    },
    /**
     * 总计数
     */
    total: {
      type: Number,
      default: 0
    },
    /**
     * 是否禁用控制
     */
    disabled: {
      type: Boolean,
      default: false
    },
    /**
     * 是否正在播放
     */
    isPlaying: {
      type: Boolean,
      default: true
    },
    /**
     * 是否显示播放控制按钮
     */
    showButton: {
      type: Boolean,
      default: true
    },
    /**
     * 是否显示百分比进度条
     */
    showPercentage: {
      type: Boolean,
      default: true
    },
    /**
     * 是否允许更改时间间隔
     */
    intervalChangeEnabled: {
      type: Boolean,
      default: true
    },
    /**
     * 时间间隔选项
     */
    intervalOptions: {
      type: Array,
      default: () => [
        { value: 24, label: '24h' },
        { value: 12, label: '12h' },
        { value: 6, label: '6h' }
      ]
    },
    /**
     * 当前时间间隔值
     */
    intervalValue: {
      type: Number,
      default: 24
    }
  },
  data () {
    return {
      percentage: 0
    }
  },
  watch: {
    count (newValue) {
      this.updatePercentage()
    },
    total (newValue) {
      this.updatePercentage()
    }
  },
  methods: {
    /**
     * 更新百分比
     */
    updatePercentage () {
      if (this.count === this.total) {
        this.percentage = 100
      } else if (this.count === 0 || this.total === 0) {
        this.percentage = 0
      } else {
        this.percentage = Number(((this.count / this.total) * 100).toFixed(0))
      }
    },

    /**
     * 格式化提示文本
     */
    formatTooltip (val) {
      return val + '%'
    },

    /**
     * 进度条变化处理
     */
    changeProgress () {
      this.$emit('progress-change', this.percentage)
    },

    /**
     * 开始播放
     */
    start () {
      this.$emit('start')
    },

    /**
     * 暂停播放
     */
    stop () {
      this.$emit('stop')
    },

    /**
     * 继续播放
     */
    continuePlay () {
      this.$emit('continue')
    },

    /**
     * 时间间隔变化处理
     */
    changeInterval () {
      this.$emit('interval-change', this.intervalValue)
    }
  }
}
</script>

<style scoped>
.map-control-panel {
  width: 100%;
  z-index: 150;
  height: 120px;
  padding: 20px 5px 20px 5px;
  border-radius: 5%;
  background-color: rgba(0, 0, 0, 0.1);
}

.el-button--small {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
}

.control-button {
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.progressStyle {
  width: 220px;
  display: inline-block;
  height: 20px;
  margin-right: 10px;
  margin-left: 20px;
}

.interval {
  margin-top: 0px;
  max-height: 30px;
  width: 100px;
}

.el-select {
  width: 80px;
}

.el-select > .el-input {
  background: transparent !important;
}

:deep(.el-input__inner) {
  background: transparent !important;
  border: 1px solid rgba(18, 137, 221, 1);
  color: #fff;
}

:deep(.el-progress__text) {
  color: #fff !important;
}

.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  background: transparent;
  border: 1px solid rgba(18, 137, 221, 1);
}

:deep(.el-input__inner) {
  height: 30px;
}

:deep(.el-select .el-input .el-select__caret) {
  position: absolute;
  top: 6px;
  right: 10px;
}

:deep(.el-select .el-input .el-select__caret.is-reverse) {
  position: absolute;
  top: -5px !important;
  right: 10px;
}

:deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.3);
}

.debug-info {
  margin-top: 10px;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px;
  border-radius: 5px;
  font-size: 12px;
}
</style>
