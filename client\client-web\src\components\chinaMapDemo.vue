<template>
  <div class="mapWrapper">
    <div class="chinaMap" @click="cancelName">
      <!-- 动态飞线图表 -->
      <dv-flyline-chart-enhanced
        v-if="showFlylines"
        :config="nowConfig"
        style="width: 100%; height: 100%"
      />
      <!-- 静态线条，在暂停状态下代替动态飞线 -->
      <dv-flyline-chart-enhanced
        v-else
        :config="staticConfig"
        style="width: 100%; height: 100%"
      />

      <!-- 计算单位显示 -->
      <div class="unitPos">
        <div><span class="taskNumTitle">等待任务数:</span></div>
        <div>
          <div class="top">
            <span class="topArg"></span>
            <span class="topNum">{{ Math.ceil(NJobs / NCenters / 15) }}</span>
          </div>
          <div class="rect">
            <div class="simple-linear"></div>
          </div>
          <div class="bottom">
            <span class="bottomArg"></span>
            <span class="bottomNum">0</span>
          </div>
        </div>
      </div>

      <!-- 点位交互层 -->
      <div class="pointWrapper">
        <div
          v-for="item in mapData"
          :key="item.name"
          :class="{
            'point-hover': item === hoveredNode,
            [`icon-breathe-${item.iconType}`]: item.iconType
          }"
          :style="{
            top: item.posY + 6 + 'px',
            left: item.posX + 5 + 'px',
            width: '14px',
            height: '14px',
            opacity: 0,
            cursor: 'pointer'
          }"
          class="point"
          @mouseenter="showHoverLabel(item)"
          @mouseleave="hideHoverLabel"
          @click.stop="showDetail(item, $event)"
        ></div>
      </div>

      <!-- 动画效果层 -->
      <div class="animationWrapper">
        <div
          v-for="item in mapData"
          :key="'anim-'+item.name"
          :class="{
            'point-hover': item === hoveredNode,
            [`icon-breathe-${item.iconType}`]: item.iconType
          }"
          :style="{
            top: item.posY + 6 + 'px',
            left: item.posX + 5 + 'px',
            width: '14px',
            height: '14px',
            animationDelay: item.animationDelay || '0s'
          }"
          class="point-animation"
        ></div>
      </div>

      <!-- 标签层 -->
      <div class="centerLabelsWrapper">
        <div
          v-for="item in mapData"
          v-show="item.showLabel"
          :key="'label-'+item.name"
          :class="getLabelClasses(item)"
          :style="getLabelStyles(item)"
          class="centerLabel"
        >
          {{ item.name }}
        </div>
      </div>
    </div>

    <!-- 点击详情弹窗 -->
    <div
      v-if="showMsg"
      :style="{
        top: centerMsg.posY + 35 + 'px',
        left: centerMsg.posX - 10 + 'px',
      }"
      class="taskWrapper"
    >
      <div class="taskMsg">
        <div class="taskHeader">
          <span class="taskTitle3">{{ centerMsg.name }}</span>
        </div>
        <div class="taskBody">
          <div>
            <span class="taskTitle1">等待任务数：</span>
            <span class="taskTitle2">{{ centerMsg.taskPend }} 个</span>
            <span :class="getLoadClass(centerMsg.taskPend)" class="loadIndicator"></span>
          </div>
          <div>
            <span class="taskTitle1">算力规模：</span>
            <span class="taskTitle2">{{ centerMsg.CenterNPops }} POps@FP16</span>
          </div>
          <div v-if="centerMsg.CenterNCards">
            <span class="taskTitle1">加速卡数：</span>
            <span class="taskTitle2">{{ centerMsg.CenterNCards }} 卡</span>
          </div>
          <div v-if="centerMsg.GraphicsCardType">
            <span class="taskTitle1">加速卡型号：</span>
            <span class="taskTitle2">{{ centerMsg.GraphicsCardType }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 悬停标签 -->
    <div
      v-if="showHoverMsg"
      :style="getHoverStyles()"
      class="hoverLabel"
    >
      <div class="hoverContent">
        <span>{{ hoverMsg.name }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { getCenter } from '@/api/screenService.js'
import { centerMsg, cityMsg } from '@/utils/config'
import flylineGenerator from '@/utils/flylineGenerator'

// 导入点位图标
const POINT_ICONS = {
  zero: require('../assets/icon_dot-1.svg'),
  icon1: require('../assets/icon_dot-1.svg'),
  icon2: require('../assets/icon_dot-2.svg'),
  icon3: require('../assets/icon_dot-3.svg'),
  icon4: require('../assets/icon_dot-4.svg'),
  icon5: require('../assets/icon_dot-5.svg'),
  icon6: require('../assets/icon_dot-6.svg'),
  icon7: require('../assets/icon_dot-7.svg'),
  icon8: require('../assets/icon_dot-8.svg'),
  icon9: require('../assets/icon_dot-9.svg'),
  icon10: require('../assets/icon_dot-10.svg')
}

// 区域配置
const REGION_CONFIG = {
  // 广东地区边界大致范围
  guangdong: {
    minX: 0.65,
    maxX: 0.75,
    minY: 0.50,
    maxY: 0.60,
    // 广东地区重要中心
    importantCenters: ['广州', '深圳', '珠海', '汕头', '佛山']
  }
}

// 飞线图表默认配置
const DEFAULT_FLYLINE_CONFIG = {
  points: [],
  lines: [],
  animation: true,
  k: 0.5,
  curvature: 5,
  bgImgSrc: null,
  centerPoint: null,
  lineWidth: 5,
  orbitColor: '#103050',
  flylineColor: '#0184d3',
  text: { fontSize: 12 },
  halo: {
    show: true,
    color: '#006400',
    duration: 1000
  }
}

// 标签布局常量
const LABEL_CONSTANTS = {
  COLLISION_WIDTH: 70,
  COLLISION_HEIGHT: 20,
  DEFAULT_OFFSET_Y: 15,
  MAX_CONNECTIONS_PER_CENTER: 4,
  MAX_ZERO_TASK_CONNECTIONS: 2,
  GD_IMPORTANT_LABEL_OFFSET_X: 10,
  GD_IMPORTANT_LABEL_OFFSET_Y: 15,
  GD_IMPORTANT_LABEL_OPACITY: 1,
  GD_NON_IMPORTANT_LABEL_OFFSET_X: 10,
  GD_NON_IMPORTANT_LABEL_OFFSET_Y: 15,
  GD_NON_IMPORTANT_LABEL_OPACITY: 0.7
}

export default {
  name: 'chinaMap',
  props: {
    Count: {
      type: Number,
      default: 0
    },
    Stop: {
      type: Boolean,
      default: false
    },
    // 接收父组件传递的接口响应数据
    taskResponse: {
      type: Object,
      default: null
    },
    // 接收父组件传递的时间间隔设置
    intervalSetting: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      // 地图数据相关
      mapData: [], // 当前帧的地图数据
      tempData: [], // 存储所有帧的数据
      taskCenter: null, // 计算中心位置数据
      cityData: [], // 城市数据

      // 展示控制
      showMsg: false, // 是否显示点击详情
      showHoverMsg: false, // 是否显示悬停标签
      showFlylines: true, // 是否显示动态飞线

      // 交互状态
      hoveredNode: null, // 当前悬停的节点
      centerMsg: this.createEmptyCenterMsg(), // 当前选中的中心详情
      hoverMsg: { // 悬停信息
        name: '',
        InfoName: '',
        posX: 0,
        posY: 0
      },

      // 动画控制
      count: 0, // 当前帧计数
      total: 0, // 总帧数
      timer: null, // 动画计时器
      stopped: true, // 是否暂停

      // 飞线图表配置
      nowConfig: { ...DEFAULT_FLYLINE_CONFIG }, // 当前飞线配置
      staticConfig: null, // 静态飞线配置(暂停时使用)

      // 任务和中心数据
      NJobs: 10, // 任务总数
      NCenters: 10, // 中心总数

      // 标签布局相关
      labelPositionsFixed: false, // 标记标签位置是否已固定
      fixedLabelPositions: {}, // 存储固定的标签位置

      // 工具属性
      randomSeed: null, // 随机数种子
      _lastPausedState: null, // 暂停状态缓存
      nowCenter: '', // 当前选中的中心名称

      // 缓存相关
      locationCache: null, // 缓存location数据，避免重复请求
      locationCacheTime: null // 缓存时间戳
    }
  },
  computed: {
    taskId () {
      return this.$store.state.id
    },
    lastTime () {
      return this.$store.state.lastTime
    },
    interval () {
      return this.$store.state.interval
    }
  },
  watch: {
    // 监听任务ID变化
    taskId (newValue) {
      if (this.nowConfig?.points?.length > 0) {
        // taskId变更，保持现有地图数据
      } else {
        this.initDemoData()
        this.showDot()
      }
    },

    // 监听父组件传递的接口数据变化
    taskResponse: {
      handler (newValue, oldValue) {
        if (newValue && (!oldValue || newValue.ID !== oldValue.ID)) {
          // 接收到父组件新的任务数据
          this.processJobDetailData(newValue)
        }
      },
      deep: true
    },

    // 监听帧计数变化
    Count (newValue) {
      this.count = newValue

      // 如果到达最后一帧
      if (this.count === this.total) {
        this.stopped = true

        if (this.tempData?.length > 0) {
          this.updateMapWithCurrentFrame()
        } else if (!this.mapData?.length) {
          this.initDemoData()
          this.applyLabelLayout()
          this.ensureLabelsVisible()
          this.showDot()
        }
      }

      // 重置显示状态
      if (this.count === 0) {
        this.showMsg = false
      }
    },

    // 监听暂停状态变化
    Stop (newValue, oldValue) {
      if (newValue !== oldValue) {
        if (newValue === true) {
          // 暂停处理
          this.handlePause()
        } else {
          // 继续处理
          this.handleResume()
        }
      }
    },

    // 监听内部暂停状态变化
    stopped (newValue) {
      if (this.stopped) {
        clearInterval(this.timer)
      } else {
        // 初始化或继续动画
        if (!this.nowConfig.points?.length) {
          this.initDemoData()
          this.showDot()
        } else if (this.tempData?.length > 0 && this.count < this.total) {
          if (!this.timer) {
            this.resumeAnimation()
          }
        }

        // 获取任务数据
        if (this.taskId !== 0 && (!this.tempData || this.tempData.length === 0)) {
          this.getCenter()
        }
      }
    }
  },
  mounted () {
    this.count = this.Count
    this.taskCenter = centerMsg().points
    this.cityData = cityMsg()

    // 初始化随机数种子
    this.initRandomSeed()

    // 获取计算中心数据
    this.getCenter()
  },
  methods: {
    //= =========================
    // 辅助方法
    //= =========================

    // 创建空的中心消息对象
    createEmptyCenterMsg () {
      return {
        name: '',
        taskPend: 0,
        CenterIdInTask: 0,
        CenterNCards: 0,
        CenterNNodes: 0,
        CenterNPops: 0,
        GraphicsCardType: '',
        posX: 0,
        posY: 0,
        InfoName: ''
      }
    },

    // 获取标签样式类
    getLabelClasses (item) {
      return {
        'label-important': item.isImportant,
        'label-high-load': !item.isImportant && item.taskPend > (this.NJobs / this.NCenters / 3),
        'label-hover': item === this.hoveredNode
      }
    },

    // 获取标签样式
    getLabelStyles (item) {
      return {
        top: (item.posY + item.labelOffsetY) + 'px',
        left: (item.posX + item.labelOffsetX) + 'px',
        opacity: item.labelOpacity || 1,
        fontSize: this.getLabelFontSize(item) + 'px'
      }
    },

    // 获取悬停标签样式
    getHoverStyles () {
      return {
        top: this.hoverMsg.posY - 45 + 'px',
        left: this.hoverMsg.posX - (this.hoverMsg.name && this.hoverMsg.name.length > 5 ? 60 : 40) + 'px'
      }
    },

    //= =========================
    // 工具方法
    //= =========================

    // 初始化随机数种子
    initRandomSeed () {
      const now = new Date()
      this.randomSeed = now.getMinutes()
    },

    // 基于种子的随机数生成
    seededRandom (max, min = 0) {
      this.randomSeed = (this.randomSeed * 9301 + 49297) % 233280
      const rnd = this.randomSeed / 233280
      return min + rnd * (max - min)
    },

    // 根据任务数获取负载样式类
    getLoadClass (taskPend) {
      const standard = this.NJobs / this.NCenters / 15 / 10

      if (taskPend === 0) return 'load-zero'
      if (taskPend < standard * 2) return 'load-low'
      if (taskPend < standard * 5) return 'load-medium'
      if (taskPend < standard * 8) return 'load-high'
      return 'load-veryhigh'
    },

    // 根据任务数获取点位图标
    getPointImage (taskPend) {
      const standard = this.NJobs / this.NCenters / 15 / 10

      if (taskPend === 0) return POINT_ICONS.zero
      if (taskPend < standard) return POINT_ICONS.icon1
      if (taskPend < standard * 2) return POINT_ICONS.icon2
      if (taskPend < standard * 3) return POINT_ICONS.icon3
      if (taskPend < standard * 4) return POINT_ICONS.icon4
      if (taskPend < standard * 5) return POINT_ICONS.icon5
      if (taskPend < standard * 6) return POINT_ICONS.icon6
      if (taskPend < standard * 7) return POINT_ICONS.icon7
      if (taskPend < standard * 8) return POINT_ICONS.icon8
      if (taskPend < standard * 9) return POINT_ICONS.icon9
      return POINT_ICONS.icon10
    },

    // 根据节点类型获取标签字体大小
    getLabelFontSize (item) {
      if (item.isImportant) return 11
      if (item.taskPend > (this.NJobs / this.NCenters / 3)) return 10.5
      return 10
    },

    // 从配置中查找计算中心点
    findConfigCenterPoint (centerName) {
      const baseConfig = centerMsg()
      return baseConfig.points.find(point => point.name === centerName)
    },

    // 查找城市像素坐标
    findCityMsgPixelCoordinate (centerName) {
      const cities = cityMsg()
      return cities.find(city => city.name === centerName)
    },

    //= =========================
    // 动画控制方法
    //= =========================

    // 处理暂停动画
    handlePause () {
      this.stopped = true
      clearInterval(this.timer)

      // 暂停飞线动画
      this.nowConfig.animation = false
      this.nowConfig = { ...this.nowConfig }

      // 创建静态线条配置
      this.staticConfig = this.createStaticConfig()

      // 隐藏动态飞线，显示静态线条
      this.showFlylines = false

      // 保存当前状态，用于恢复
      if (this.mapData?.length > 0) {
        this._lastPausedState = {
          count: this.count,
          mapData: JSON.parse(JSON.stringify(this.mapData)),
          tempData: this.tempData
        }
      }

      // 确保标签在暂停状态下保持可见
      this.ensureLabelsVisible()
    },

    // 创建静态配置
    createStaticConfig () {
      const staticConfig = JSON.parse(JSON.stringify(this.nowConfig))
      staticConfig.animation = false

      // 确保所有飞线完全静态化
      if (staticConfig.lines?.length > 0) {
        staticConfig.lines.forEach(line => {
          if (line.effect) line.effect.show = false
          if (line.duration) line.duration = [99999, 99999]
        })
      }

      // 处理点的动画效果
      if (staticConfig.points?.length > 0) {
        staticConfig.points.forEach(point => {
          if (point.halo) point.halo.show = false
        })
      }

      return staticConfig
    },

    // 处理继续动画
    handleResume () {
      this.stopped = false

      // 恢复飞线动画
      this.nowConfig.animation = true
      this.nowConfig = { ...this.nowConfig }

      // 显示动态飞线，隐藏静态线条
      this.showFlylines = true

      // 检查是否有保存的状态
      if (this._lastPausedState && this._lastPausedState.count === this.count) {
        this.mapData = this._lastPausedState.mapData
        this.showDot()
      }

      // 如果有临时数据，从当前帧继续渲染
      if (this.tempData?.length > 0 && this.count < this.total) {
        this.resumeAnimation()
      }
    },

    // 继续播放动画
    resumeAnimation () {
      if (this.stopped || !this.tempData?.length || this.count >= this.total) {
        return
      }

      clearInterval(this.timer)

      // 首先用当前帧数据更新地图
      this.updateMapWithCurrentFrame()

      // 然后开始定时更新后续帧
      this.timer = setInterval(() => {
        // 检查是否已经暂停
        if (this.stopped) {
          clearInterval(this.timer)
          this.timer = null
          return
        }

        this.count++
        if (this.count >= this.total) {
          this.count = this.total
          clearInterval(this.timer)
          this.timer = null

          // 在最后一帧结束时确保标签保持可见
          this.ensureLabelsVisible()
          return
        }

        this.updateMapWithCurrentFrame()
      }, 2000)
    },

    // 添加新方法：检测标签是否发生碰撞
    checkLabelCollision (label1, label2, labelWidth = LABEL_CONSTANTS.COLLISION_WIDTH, labelHeight = LABEL_CONSTANTS.COLLISION_HEIGHT) {
      // 检查标签是否显示，如果不显示则不会碰撞
      if (!label1.showLabel || !label2.showLabel) {
        return false
      }

      // 根据标签长度动态调整碰撞检测宽度
      const l1Width = Math.max(labelWidth, label1.name.length * 8)
      const l2Width = Math.max(labelWidth, label2.name.length * 8)

      // 计算标签1边界
      const l1Left = label1.posX + label1.labelOffsetX - l1Width / 2
      const l1Right = label1.posX + label1.labelOffsetX + l1Width / 2
      const l1Top = label1.posY + label1.labelOffsetY - labelHeight / 2
      const l1Bottom = label1.posY + label1.labelOffsetY + labelHeight / 2

      // 计算标签2边界
      const l2Left = label2.posX + label2.labelOffsetX - l2Width / 2
      const l2Right = label2.posX + label2.labelOffsetX + l2Width / 2
      const l2Top = label2.posY + label2.labelOffsetY - labelHeight / 2
      const l2Bottom = label2.posY + label2.labelOffsetY + labelHeight / 2

      // 检查是否重叠
      return !(l1Right < l2Left || l1Left > l2Right || l1Bottom < l2Top || l1Top > l2Bottom)
    },
    // 添加新方法：保存固定的标签位置
    saveFixedLabelPositions () {
      if (!this.mapData || this.mapData.length === 0) return

      this.fixedLabelPositions = {}

      // 保存每个节点的标签位置
      this.mapData.forEach(item => {
        this.fixedLabelPositions[item.name] = {
          offsetX: item.labelOffsetX,
          offsetY: item.labelOffsetY,
          showLabel: item.showLabel,
          isImportant: item.isImportant,
          inGuangdong: item.inGuangdong,
          labelOpacity: item.labelOpacity
        }
      })

      // 标记标签位置已固定
      this.labelPositionsFixed = true
    },

    // 修改悬停标签显示方法
    showHoverLabel (item) {
      this.hoverMsg = {
        name: item.name,
        InfoName: item.InfoName,
        posX: item.posX,
        posY: item.posY
      }
      this.showHoverMsg = true

      // 设置当前悬停节点
      this.hoveredNode = item

      // 自动显示被悬停节点的标签
      if (!item.showLabel) {
        item._originalShowLabel = item.showLabel
        item.showLabel = true
      }

      // 更新showDot以显示悬停效果
      this.showDot()

      this.$nextTick(() => {
        const hoverLabel = document.querySelector('.hoverLabel')
        if (hoverLabel) {
          hoverLabel.classList.add('hoverLabel-active')
        }
      })
    },

    // 修改隐藏悬停标签方法
    hideHoverLabel () {
      const hoverLabel = document.querySelector('.hoverLabel')
      if (hoverLabel) {
        hoverLabel.classList.remove('hoverLabel-active')
      }

      // 如果有悬停节点，恢复其原始显示状态
      if (this.hoveredNode) {
        if (this.hoveredNode._originalShowLabel !== undefined) {
          this.hoveredNode.showLabel = this.hoveredNode._originalShowLabel
          delete this.hoveredNode._originalShowLabel
        }
        this.hoveredNode = null

        // 更新showDot以移除悬停效果
        this.showDot()
      }

      setTimeout(() => {
        this.showHoverMsg = false
      }, 150)
    },
    // 修改更新帧数据方法，防止重新计算标签位置
    updateMapWithCurrentFrame () {
      if (!this.tempData?.length || this.count >= this.total) {
        return
      }

      this.mapData = []
      try {
        this.tempData.forEach(item => {
          // 添加安全检查
          if (!item.SnapshotInfoToWebList?.[this.count]) {
            return
          }

          // 获取保存的标签位置信息
          const savedLabelPos = this.fixedLabelPositions[item.name] || {
            offsetX: 0,
            offsetY: LABEL_CONSTANTS.DEFAULT_OFFSET_Y,
            showLabel: true,
            isImportant: false,
            inGuangdong: false,
            labelOpacity: 1
          }

          this.mapData.push({
            name: item.name,
            x: item.x,
            y: item.y,
            taskPend: item.SnapshotInfoToWebList[this.count].PendingJob,
            CenterIdInTask: item.CenterIdInTask,
            CenterNCards: item.CenterNCards,
            CenterNNodes: item.CenterNNodes,
            CenterNPops: item.CenterNPops,
            GraphicsCardType: item.GraphicsCardType,
            posX: item.posX,
            posY: item.posY,
            apiName: item.apiName,
            InfoName: item.InfoName,
            // 使用保存的标签位置
            showLabel: savedLabelPos.showLabel,
            labelOffsetX: savedLabelPos.offsetX,
            labelOffsetY: savedLabelPos.offsetY,
            isImportant: savedLabelPos.isImportant,
            labelOpacity: savedLabelPos.labelOpacity,
            inGuangdong: savedLabelPos.inGuangdong
          })
        })

        // 只有在第一次加载或标签位置未固定时应用标签布局算法
        if (!this.labelPositionsFixed) {
          this.applyLabelLayout()
        }

        // 如果是最后一帧，确保标签可见
        if (this.count === this.total - 1) {
          this.ensureLabelsVisible()
        }

        this.showDot()
      } catch (err) {
        // 更新当前帧时出错，忽略此次更新
      }
    },

    // 修改processJobDetailData方法，只在首次加载时设置标签位置
    processJobDetailData (res) {
      this.NJobs = res.NJobs || 1
      this.NCenters = res.NCenters || 1

      this.total = res.CenterInfoToWebList[0]?.SnapshotInfoToWebList?.length || 0
      if (this.total === 0) {
        return
      }

      const containerWidth = 1100
      const containerHeight = 950
      this.tempData = []

      res.CenterInfoToWebList.forEach(item => {
        let centerPoint = this.taskCenter.find(center => center.name === item.Location.Name)

        if (!centerPoint) {
          const baseConfig = centerMsg()
          centerPoint = baseConfig.points.find(point => point.name === item.Location.Name)
        }

        if (centerPoint) {
          const coordinate = Array.isArray(centerPoint.coordinate)
            ? centerPoint.coordinate
            : [0.5, 0.5]

          const cityPixelCoordinates = this.findCityMsgPixelCoordinate(item.Location.Name)
          let posX, posY

          if (cityPixelCoordinates) {
            posX = cityPixelCoordinates.x
            posY = cityPixelCoordinates.y
          } else {
            posX = coordinate[0] * containerWidth
            posY = coordinate[1] * containerHeight
          }

          // 使用配置中的原始名称
          const originalName = centerPoint.name

          this.tempData.push({
            name: originalName,
            x: coordinate[0],
            y: coordinate[1],
            posX: posX,
            posY: posY,
            taskNum: item.CenterIdInTask,
            SnapshotInfoToWebList: item.SnapshotInfoToWebList || [],
            CenterIdInTask: item.CenterIdInTask,
            CenterNCards: item.CenterNCards,
            CenterNNodes: item.CenterNNodes,
            CenterNPops: item.CenterNPops,
            GraphicsCardType: item.GraphicsCardType,
            apiName: item.Location.Name,
            InfoName: item.InfoName,
            showLabel: true,
            labelOffsetX: 0,
            labelOffsetY: LABEL_CONSTANTS.DEFAULT_OFFSET_Y,
            isImportant: false
          })
        }
      })

      // 重置标签位置固定状态
      this.labelPositionsFixed = false
      this.fixedLabelPositions = {}

      this.mapData = []

      if (this.tempData.length > 0 && this.count < this.total) {
        this.updateMapWithCurrentFrame()
        this.resumeAnimation()
      }
    },
    showDot () {
      if (!this.mapData || this.mapData.length === 0) {
        this.initDemoData()
      }

      this.nowConfig.points = []
      this.nowConfig.lines = []

      const standrad = this.NJobs / this.NCenters / 15 / 10

      this.mapData.forEach((item) => {
        // 使用getPointImage方法确保一致使用相同的图标逻辑
        const iconSrc = this.getPointImage(item.taskPend)

        // 添加图标类型属性用于CSS动画
        item.iconType = this.getPointIconType(item.taskPend)

        // 为每个节点生成独立的随机动画延迟，实现异步呼吸效果
        // 即使相同负载级别的节点也会有不同的呼吸节奏
        item.animationDelay = this.generateAnimationDelay(item.iconType)

        // 统一图标大小
        const iconSize = 16

        this.nowConfig.points.push({
          name: item.name,
          coordinate: [item.x, item.y],
          taskPend: item.taskPend,
          icon: {
            show: true,
            src: iconSrc,
            width: iconSize,
            height: iconSize
          },
          text: {
            show: false
          },
          halo: {
            show: false
          }
        })

        // 对于悬停状态的点位，添加特殊效果
        if (item === this.hoveredNode) {
          this.nowConfig.points.push({
            name: item.name + '-hover',
            coordinate: [item.x, item.y],
            icon: {
              show: true,
              src: iconSrc,
              width: iconSize * 1.5,
              height: iconSize * 1.5
            },
            text: {
              show: false
            },
            halo: {
              show: true,
              color: 'rgba(255, 255, 255, 0.4)',
              duration: 800,
              radius: 1.2
            }
          })
        }
      })

      // 重构飞线生成逻辑：基于SendToInfoMap而不是pending task数量
      if (this.mapData.length > 1) {
        // 使用飞线生成器生成飞线
        const generatedFlylines = flylineGenerator.generateFlylinesFromSendToInfoMap({
          taskResponse: this.taskResponse,
          tempData: this.tempData,
          mapData: this.mapData,
          count: this.count
        })

        // 将生成的飞线添加到配置中
        this.nowConfig.lines.push(...generatedFlylines)

        // 添加数据结构日志
        flylineGenerator.logDataStructure({
          taskResponse: this.taskResponse,
          tempData: this.tempData,
          mapData: this.mapData,
          count: this.count,
          total: this.total
        })
      }

      this.mapData.forEach((item) => {
        if (item.name === this.nowCenter) {
          this.centerMsg = item
        }
      })

      this.nowConfig = { ...this.nowConfig }

      // 同时更新静态配置
      this.staticConfig = JSON.parse(JSON.stringify(this.nowConfig))
      this.staticConfig.animation = false

      // 确保所有飞线完全静态化
      if (this.staticConfig.lines && this.staticConfig.lines.length > 0) {
        this.staticConfig.lines.forEach(line => {
          // 禁用线条动画效果
          if (line.effect) {
            line.effect.show = false
          }
          // 设置非常大的周期，实质上停止动画
          if (line.duration) {
            line.duration = [99999, 99999]
          }
        })
      }

      // 同样处理点的动画效果
      if (this.staticConfig.points && this.staticConfig.points.length > 0) {
        this.staticConfig.points.forEach(point => {
          if (point.halo) {
            point.halo.show = false
          }
        })
      }

      // 确保当没有飞线时，创建一条不可见的飞线以保持图标显示
      if (this.nowConfig.lines.length === 0 && this.mapData.length > 1) {
        // 添加一条不可见飞线以确保图标正常显示
        // 选择前两个点位创建一条完全透明的线
        const source = this.mapData[0]
        const target = this.mapData[1]

        const invisibleLine = {
          source: source.name,
          target: target.name,
          width: 0.1, // 非常细的线
          color: 'rgba(0, 0, 0, 0)', // 完全透明
          orbitColor: 'rgba(0, 0, 0, 0)', // 完全透明
          duration: [99999, 99999], // 极长的周期，实际上不移动
          radius: 0.1, // 非常小的粒子
          k: 0.1, // 非常慢的速度
          curvature: 0.1,
          num: 0 // 无粒子
        }

        this.nowConfig.lines.push(invisibleLine)

        // 确保静态配置也有这条不可见的线
        this.staticConfig = JSON.parse(JSON.stringify(this.nowConfig))
        this.staticConfig.animation = false
      }
    },

    /**
     * 根据任务数获取点位图标类型
     * 将11个图标类型合并为5个动画组，简化CSS结构
     * @param {number} taskPend 等待任务数
     * @returns {string} 图标类型 (zero|low|medium|high|extreme|maximum)
     */
    getPointIconType (taskPend) {
      const standard = this.NJobs / this.NCenters / 15 / 10

      if (taskPend === 0) return 'zero' // 零负载
      if (taskPend < standard * 3) return 'low' // 低负载 (icon1-3)
      if (taskPend < standard * 5) return 'medium' // 中负载 (icon4-5)
      if (taskPend < standard * 7) return 'high' // 高负载 (icon6-7)
      if (taskPend < standard * 9) return 'extreme' // 极高负载 (icon8-9)
      return 'maximum' // 最高负载 (icon10)
    },

    /**
     * 为每个节点生成随机的动画延迟，实现异步呼吸效果
     * @param {string} iconType 图标类型
     * @returns {string} CSS动画延迟值，格式为 "-X.XXs"
     */
    generateAnimationDelay (iconType) {
      // 动画周期配置：负载越高，呼吸越快
      const animationDurations = {
        zero: 8, // 零负载：8秒周期
        low: 6.5, // 低负载：6.5秒周期
        medium: 5, // 中负载：5秒周期
        high: 4, // 高负载：4秒周期
        extreme: 3, // 极高负载：3秒周期
        maximum: 2.5 // 最高负载：2.5秒周期
      }

      const duration = animationDurations[iconType] || 5
      // 生成0到动画周期之间的随机延迟，使用负值实现异步效果
      const randomDelay = Math.random() * duration
      return `-${randomDelay.toFixed(2)}s`
    },

    /**
     * 根据名称查找中心
     * @param {string} centerName 中心名称
     * @returns {Object|null} 中心对象或null
     */
    findCenterByName (centerName) {
      return this.mapData.find(center =>
        center.name === centerName ||
        center.InfoName === centerName ||
        center.apiName === centerName
      )
    },

    getCenter () {
      if (this._preventReinitOnNextUpdate && this.nowConfig?.points?.length > 0) {
        this._preventReinitOnNextUpdate = false
        return
      }

      // 检查缓存，避免重复请求
      const now = Date.now()
      const cacheExpireTime = 5 * 60 * 1000 // 5分钟缓存

      if (this.locationCache &&
          this.locationCacheTime &&
          (now - this.locationCacheTime) < cacheExpireTime) {
        this.processLocationData(this.locationCache)
        return
      }

      const hasExistingMap = this.nowConfig?.points?.length > 0

      getCenter().then((res) => {
        // 缓存响应数据
        this.locationCache = res
        this.locationCacheTime = now

        this.processLocationData(res)
      }).catch(error => {
        if (!hasExistingMap) {
          this.initDemoData()
          this.showDot()
        }
      })
    },

    // 新增方法：处理location数据
    processLocationData (res) {
      const hasExistingMap = this.nowConfig?.points?.length > 0

      this.taskCenter = []
      res.LocationInfoToWebList.forEach((item) => {
        const configCenterPoint = this.findConfigCenterPoint(item.Name)

        this.taskCenter.push({
          name: item.Name,
          coordinate: configCenterPoint ? configCenterPoint.coordinate : [item.CoordinateX, item.CoordinateY],
          CenterNCards: item.CenterNCards || 0,
          CenterNNodes: item.CenterNNodes || 0,
          CenterNPopsL: item.CenterNPops || 0,
          GraphicsCardType: item.GraphicsCardType || ''
        })
      })

      this.cityData = []
      const city = cityMsg()
      this.taskCenter.forEach((item) => {
        city.forEach((Item) => {
          if (item.name === Item.name) {
            this.cityData.push(Item)
          }
        })
      })

      if (this.taskId !== 0) {
        if (hasExistingMap) {
          // 保持现有地图，等待父组件传递任务数据
        } else {
          this.initMapWithRealCenters()
        }
        // 移除自动请求任务详情的逻辑，让父组件主动控制何时获取数据
        // this.$emit('need-job-detail', this.taskId)
      } else {
        if (!hasExistingMap) {
          this.initMapWithRealCenters()
        }
      }
    },

    // 添加getJobDetail方法供父组件调用
    getJobDetail (taskId) {
      // 发射事件通知父组件获取任务详细数据
      this.$emit('need-job-detail', taskId)
    },

    initMapWithRealCenters () {
      this.NJobs = 100
      this.NCenters = this.taskCenter?.length || 10
      this.mapData = []

      const containerWidth = 1100
      const containerHeight = 950

      if (this.taskCenter?.length > 0) {
        this.taskCenter.forEach((item, index) => {
          // 使用基于种子的随机数
          const shouldHaveTask = this.seededRandom(1) > 0.3 // 70%的节点有任务
          const baseTaskPend = shouldHaveTask ? Math.floor(this.seededRandom(30)) : 0
          const additionalTaskPend = (shouldHaveTask && this.seededRandom(1) > 0.7) ? Math.floor(this.seededRandom(50)) : 0
          const taskPend = baseTaskPend + additionalTaskPend

          const cityPixelCoordinates = this.findCityMsgPixelCoordinate(item.name)
          let posX, posY

          if (cityPixelCoordinates) {
            posX = cityPixelCoordinates.x
            posY = cityPixelCoordinates.y
          } else {
            posX = item.coordinate[0] * containerWidth
            posY = item.coordinate[1] * containerHeight
          }

          this.mapData.push({
            name: item.name,
            x: item.coordinate[0],
            y: item.coordinate[1],
            taskPend: taskPend,
            CenterIdInTask: index,
            CenterNCards: item.CenterNCards || Math.floor(Math.random() * 50) + 10,
            CenterNNodes: item.CenterNNodes || Math.floor(Math.random() * 20) + 5,
            CenterNPops: item.CenterNPopsL || (Math.random() * 1000 + 500).toFixed(0),
            GraphicsCardType: item.GraphicsCardType || 'A100',
            posX: posX,
            posY: posY,
            apiName: item.name,
            InfoName: null,
            showLabel: true,
            labelOffsetX: 0,
            labelOffsetY: LABEL_CONSTANTS.DEFAULT_OFFSET_Y,
            isImportant: false
          })
        })

        // 应用标签布局算法
        this.applyLabelLayout()

        this.showDot()
      } else {
        this.initDemoData()
        this.showDot()
      }
    },
    initDemoData () {
      this.NJobs = 100
      this.NCenters = 10
      this.mapData = []

      const containerWidth = 1100
      const containerHeight = 950

      const baseConfig = centerMsg()

      if (this.taskCenter && this.taskCenter.length > 0) {
        this.taskCenter.forEach((item, index) => {
          // 使用基于种子的随机数
          const shouldHaveTask = this.seededRandom(1) > 0.3 // 70%的节点有任务
          const baseTaskPend = shouldHaveTask ? Math.floor(this.seededRandom(30)) : 0
          const additionalTaskPend = (shouldHaveTask && this.seededRandom(1) > 0.7) ? Math.floor(this.seededRandom(50)) : 0
          const taskPend = baseTaskPend + additionalTaskPend

          const cityPixelCoordinates = this.findCityMsgPixelCoordinate(item.name)
          let posX, posY

          if (cityPixelCoordinates) {
            posX = cityPixelCoordinates.x
            posY = cityPixelCoordinates.y
          } else {
            posX = item.coordinate[0] * containerWidth
            posY = item.coordinate[1] * containerHeight
          }

          this.mapData.push({
            name: item.name, // 使用原始名称
            x: item.coordinate[0],
            y: item.coordinate[1],
            taskPend: taskPend,
            CenterIdInTask: index,
            CenterNCards: item.CenterNCards || Math.floor(Math.random() * 50) + 10,
            CenterNNodes: item.CenterNNodes || Math.floor(Math.random() * 20) + 5,
            CenterNPops: item.CenterNPopsL || (Math.random() * 1000 + 500).toFixed(0),
            GraphicsCardType: item.GraphicsCardType || 'A100',
            posX: posX,
            posY: posY,
            apiName: item.name, // 保存原始名称的副本
            InfoName: null, // 不使用InfoName
            // 添加标签布局所需的属性
            showLabel: true,
            labelOffsetX: 0,
            labelOffsetY: 15,
            isImportant: false
          })
        })
        this.showDot()
      } else if (baseConfig && baseConfig.points) {
        baseConfig.points.slice(1).forEach((item, index) => {
          // 使用基于种子的随机数
          const shouldHaveTask = this.seededRandom(1) > 0.3 // 70%的节点有任务
          const taskPend = shouldHaveTask ? Math.floor(this.seededRandom(60)) : 0

          const cityPixelCoordinates = this.findCityMsgPixelCoordinate(item.name)
          let posX, posY

          if (cityPixelCoordinates) {
            posX = cityPixelCoordinates.x
            posY = cityPixelCoordinates.y
          } else {
            posX = item.coordinate[0] * containerWidth
            posY = item.coordinate[1] * containerHeight
          }

          this.mapData.push({
            name: item.name, // 使用原始名称
            x: item.coordinate[0],
            y: item.coordinate[1],
            taskPend: taskPend,
            CenterIdInTask: index,
            CenterNCards: Math.floor(Math.random() * 50) + 10,
            CenterNNodes: Math.floor(Math.random() * 20) + 5,
            CenterNPops: (Math.random() * 1000 + 500).toFixed(0),
            GraphicsCardType: 'A100',
            posX: posX,
            posY: posY,
            apiName: item.name, // 保存原始名称的副本
            InfoName: null, // 不使用InfoName
            // 添加标签布局所需的属性
            showLabel: true,
            labelOffsetX: 0,
            labelOffsetY: 15,
            isImportant: false
          })
        })
        this.showDot()
      }
      // 在初始化演示数据后应用标签布局
      this.applyLabelLayout()
    },
    showDetail (a, b) {
      this.nowCenter = a.name
      this.mapData.forEach((item) => {
        if (item.name === this.nowCenter) {
          this.centerMsg = item
        }
      })
      if (this.nowCenter !== '') {
        this.showMsg = true
      } else {
        this.showMsg = false
      }
      this.showHoverMsg = false
    },
    cancelName () {
      this.showMsg = false
    },
    // 添加确保标签在渲染结束后保持可见的函数
    ensureLabelsVisible () {
      if (!this.mapData || this.mapData.length === 0) return

      // 确保所有节点的标签始终可见
      this.mapData.forEach(item => {
        // 所有标签都设为可见，包括零任务标签
        item.showLabel = true
        // 确保所有标签的不透明度一致
        item.labelOpacity = 1
      })

      // 应用碰撞检测，确保标签不重叠
      let iterationCount = 0
      let hasCollisions = true

      while (hasCollisions && iterationCount < 3) {
        hasCollisions = false
        iterationCount++

        for (let i = 0; i < this.mapData.length; i++) {
          const item1 = this.mapData[i]
          if (!item1.showLabel) continue

          for (let j = i + 1; j < this.mapData.length; j++) {
            const item2 = this.mapData[j]
            if (!item2.showLabel) continue

            if (this.checkLabelCollision(item1, item2)) {
              hasCollisions = true

              // 广东地区内部的碰撞处理
              if (item1.inGuangdong && item2.inGuangdong) {
                // 非重要节点相互碰撞
                if (!item1.isImportant && !item2.isImportant) {
                  if (item1.taskPend < item2.taskPend) {
                    item1.showLabel = false
                  } else {
                    item2.showLabel = false
                  }
                }
                // 重要节点和非重要节点碰撞
                else if (item1.isImportant && !item2.isImportant) {
                  item2.showLabel = false
                } else if (!item1.isImportant && item2.isImportant) {
                  item1.showLabel = false
                }
                // 两个重要节点碰撞，调整位置
                else {
                  const dx = item2.posX - item1.posX
                  const dy = item2.posY - item1.posY
                  const angle = Math.atan2(dy, dx)

                  const offsetDist = 15
                  item1.labelOffsetX -= Math.cos(angle) * offsetDist
                  item1.labelOffsetY -= Math.sin(angle) * offsetDist
                  item2.labelOffsetX += Math.cos(angle) * offsetDist
                  item2.labelOffsetY += Math.sin(angle) * offsetDist
                }
              }
              // 非广东区域的碰撞处理：尝试调整位置而不是隐藏
              else {
                const dx = item2.posX - item1.posX
                const dy = item2.posY - item1.posY
                const angle = Math.atan2(dy, dx)

                const offsetDist = 15
                item1.labelOffsetX -= Math.cos(angle) * offsetDist
                item1.labelOffsetY -= Math.sin(angle) * offsetDist
                item2.labelOffsetX += Math.cos(angle) * offsetDist
                item2.labelOffsetY += Math.sin(angle) * offsetDist
              }
            }
          }
        }
      }

      // 确保特定重要节点始终可见
      for (const centerName of REGION_CONFIG.guangdong.importantCenters) {
        const center = this.mapData.find(item => item.name === centerName)
        if (center) {
          center.showLabel = true
          center.isImportant = true
          center.labelOpacity = LABEL_CONSTANTS.GD_IMPORTANT_LABEL_OPACITY
        }
      }
    },
    //= =========================
    // 标签布局相关方法
    //= =========================

    // 应用标签布局算法
    applyLabelLayout () {
      if (!this.mapData || this.mapData.length === 0) return

      // 如果标签位置已经固定，则使用保存的位置
      if (this.labelPositionsFixed && Object.keys(this.fixedLabelPositions).length > 0) {

        // 应用保存的标签位置
        this.mapData.forEach(item => {
          if (this.fixedLabelPositions[item.name]) {
            const savedPos = this.fixedLabelPositions[item.name]
            item.labelOffsetX = savedPos.offsetX
            item.labelOffsetY = savedPos.offsetY
            item.showLabel = savedPos.showLabel
            item.isImportant = savedPos.isImportant
            item.labelOpacity = savedPos.labelOpacity
            item.inGuangdong = savedPos.inGuangdong
          } else {
            // 如果没有保存位置，使用默认位置
            this.setDefaultLabelPosition(item)
          }
        })
        return
      }

      // 重置所有标签的偏移和可见性
      this.mapData.forEach(item => this.setDefaultLabelPosition(item))

      // 检测点是否位于广东地区并标记重要中心
      this.markRegionNodes()

      // 对所有标签按优先级排序（重要 > 高任务量 > 普通）
      this.mapData.sort((a, b) => {
        if (a.isImportant && !b.isImportant) return -1
        if (!a.isImportant && b.isImportant) return 1
        return b.taskPend - a.taskPend
      })

      // 第一轮：选择性隐藏标签以减少拥挤
      this.filterVisibleLabels()

      // 第二轮：为可见标签找最佳位置
      this.findOptimalLabelPositions()

      // 第三轮：处理仍然存在的碰撞
      this.resolveRemainingCollisions()

      // 第四轮：确保重要节点标签可见
      this.ensureImportantLabelsVisible()

      // 完成布局后，保存标签位置
      this.saveFixedLabelPositions()
    },

    // 设置标签默认位置
    setDefaultLabelPosition (item) {
      item.labelOffsetX = 0
      item.labelOffsetY = 15 // 默认在点下方15px
      item.showLabel = true
      item.isImportant = false
      // 所有标签使用相同的不透明度
      item.labelOpacity = 1
      item.inGuangdong = false
    },

    // 标记区域节点
    markRegionNodes () {
      this.mapData.forEach(item => {
        // 检查是否在广东区域内
        const gd = REGION_CONFIG.guangdong
        if (item.x >= gd.minX && item.x <= gd.maxX &&
          item.y >= gd.minY && item.y <= gd.maxY) {
          // 标记为广东地区节点
          item.inGuangdong = true

          // 检查是否为重要中心
          if (gd.importantCenters.includes(item.name)) {
            item.isImportant = true
            item.labelOpacity = LABEL_CONSTANTS.GD_IMPORTANT_LABEL_OPACITY
          }
        } else {
          item.inGuangdong = false
        }
      })
    },

    // 筛选需要显示的标签
    filterVisibleLabels () {
      // 为广东地区的节点应用特殊标签策略
      const gdCenters = this.mapData.filter(item => item.inGuangdong)

      gdCenters.forEach((item, index) => {
        if (item.isImportant) {
          // 重要节点保持显示
          item.showLabel = true
        } else if (item.taskPend > (this.NJobs / this.NCenters / 5)) {
          // 任务量大的节点保持显示
          item.showLabel = true
        } else {
          // 只显示前几个节点，避免过多标签
          item.showLabel = index < 5
        }
      })
    },

    // 为标签寻找最佳位置
    findOptimalLabelPositions () {
      // 方向优先级策略，优先考虑更美观的方向
      const directionPriority = [
        {
          x: 0,
          y: 30
        }, // 下方（优先）
        {
          x: 35,
          y: 0
        }, // 右侧
        {
          x: -35,
          y: 0
        }, // 左侧
        {
          x: 0,
          y: -30
        }, // 上方
        {
          x: 30,
          y: 30
        }, // 右下
        {
          x: -30,
          y: 30
        }, // 左下
        {
          x: 30,
          y: -30
        }, // 右上
        {
          x: -30,
          y: -30
        } // 左上
      ]

      // 为每个标签尝试最佳位置
      this.mapData.forEach(item => {
        if (!item.showLabel) return

        // 跳过零任务量的节点标签，保持其在下方且更透明
        if (item.taskPend === 0) {
          item.labelOffsetY = 25
          item.labelOpacity = 1
          return
        }

        // 对于重要节点，优先考虑更好的位置
        let bestDirection = null
        let bestScore = -1

        // 尝试每个方向，计算得分
        for (const dir of directionPriority) {
          // 保存当前位置
          const originalX = item.labelOffsetX
          const originalY = item.labelOffsetY

          // 测试这个方向
          item.labelOffsetX = dir.x
          item.labelOffsetY = dir.y

          // 检查此位置是否与其他标签碰撞
          const hasCollision = this.checkLabelCollisions(item)

          // 如果没有碰撞，计算该位置的得分
          if (!hasCollision) {
            const score = this.calculateDirectionScore(item, dir)

            // 更新最佳方向
            if (score > bestScore) {
              bestScore = score
              bestDirection = { ...dir }
            }
          }

          // 恢复原来的位置，继续测试下一个方向
          item.labelOffsetX = originalX
          item.labelOffsetY = originalY
        }

        // 应用最佳方向或尝试更远的偏移
        if (bestDirection) {
          item.labelOffsetX = bestDirection.x
          item.labelOffsetY = bestDirection.y
        } else {
          this.tryFarOffsets(item)
        }
      })
    },

    // 检查标签与其他所有标签的碰撞
    checkLabelCollisions (item) {
      for (const other of this.mapData) {
        if (other !== item && other.showLabel && this.checkLabelCollision(item, other)) {
          return true
        }
      }
      return false
    },

    // 计算方向得分
    calculateDirectionScore (item, dir) {
      let score = 10 // 基础分

      // 根据方向给分（下方和右侧优先）
      if (dir.y > 0 && dir.x === 0) {
        score += 5
      } else if (dir.x > 0 && dir.y === 0) score += 3

      // 偏移小的优先
      score -= Math.sqrt(dir.x * dir.x + dir.y * dir.y) / 10

      // 如果是重要节点，更偏好某些方向
      if (item.isImportant) {
        if (dir.y > 0) score += 2 // 重要节点偏好标签在下方
      }

      // 如果是高负载节点，同样有特定偏好
      if (item.taskPend > (this.NJobs / this.NCenters / 3)) {
        if (dir.x !== 0) score += 1 // 高负载节点偏好左右两侧
      }

      return score
    },

    // 尝试更远的偏移位置
    tryFarOffsets (item) {
      const farOffsets = [
        {
          x: 0,
          y: 45
        },
        {
          x: 50,
          y: 0
        },
        {
          x: -50,
          y: 0
        },
        {
          x: 0,
          y: -45
        },
        {
          x: 40,
          y: 40
        },
        {
          x: -40,
          y: 40
        }
      ]

      for (const offset of farOffsets) {
        item.labelOffsetX = offset.x
        item.labelOffsetY = offset.y

        if (!this.checkLabelCollisions(item)) break
      }
    },

    // 解决剩余碰撞
    resolveRemainingCollisions () {
      let iterationCount = 0
      let hasCollisions = true

      while (hasCollisions && iterationCount < 5) {
        hasCollisions = false
        iterationCount++

        // 检查所有标签对是否有碰撞
        for (let i = 0; i < this.mapData.length; i++) {
          const item1 = this.mapData[i]
          if (!item1.showLabel) continue

          for (let j = i + 1; j < this.mapData.length; j++) {
            const item2 = this.mapData[j]
            if (!item2.showLabel) continue

            if (this.checkLabelCollision(item1, item2)) {
              hasCollisions = true
              this.resolveLabelCollision(item1, item2)
            }
          }
        }
      }
    },

    // 解决两个标签之间的碰撞
    resolveLabelCollision (item1, item2) {
      // 解决碰撞：优先保留重要节点，然后是高负载节点
      if (item1.isImportant && !item2.isImportant) {
        // 保留重要节点标签
        item2.showLabel = false
      } else if (!item1.isImportant && item2.isImportant) {
        // 保留重要节点标签
        item1.showLabel = false
      } else if (item1.inGuangdong && item2.inGuangdong) {
        // 两个都是广东区域的节点，优先保留任务量大的
        if (item1.taskPend < item2.taskPend) {
          item1.showLabel = false
        } else {
          item2.showLabel = false
        }
      } else {
        // 两个都是重要节点或都是普通节点，尝试调整位置解决碰撞
        // 计算两点之间的角度，决定最佳偏移方向
        const dx = item2.posX - item1.posX
        const dy = item2.posY - item1.posY
        const angle = Math.atan2(dy, dx)

        // 将第一个点标签向反方向偏移
        const offsetDist = 10
        item1.labelOffsetX -= Math.cos(angle) * offsetDist
        item1.labelOffsetY -= Math.sin(angle) * offsetDist

        // 将第二个点标签向正方向偏移
        item2.labelOffsetX += Math.cos(angle) * offsetDist
        item2.labelOffsetY += Math.sin(angle) * offsetDist
      }
    },

    // 确保重要节点标签可见
    ensureImportantLabelsVisible () {
      // 广东区域的重要节点必须可见
      for (const centerName of REGION_CONFIG.guangdong.importantCenters) {
        const center = this.mapData.find(item => item.name === centerName)
        if (center) {
          center.showLabel = true
          center.isImportant = true
          center.labelOpacity = LABEL_CONSTANTS.GD_IMPORTANT_LABEL_OPACITY
        }
      }
    },
    finalPassAdjustImportantLabels () {
      const importantNodes = this.mapData.filter(item => item.isImportant && item.showLabel)
      for (const importantNode of importantNodes) {
        let hasCollision = false
        for (const otherNode of this.mapData) {
          if (importantNode === otherNode || !otherNode.showLabel) continue
          if (this.checkLabelCollision(importantNode, otherNode)) {
            hasCollision = true
            break
          }
        }

        if (hasCollision) {
          // 尝试微小的偏移，看是否能改善
          const fineTuneOffsets = [{
            x: 5,
            y: 0
          }, {
            x: -5,
            y: 0
          }, {
            x: 0,
            y: 5
          }, {
            x: 0,
            y: -5
          }] // 修正中文变量名
          const originalOffsetX = importantNode.labelOffsetX
          const originalOffsetY = importantNode.labelOffsetY

          for (const offset of fineTuneOffsets) { // 使用修正后的变量名
            importantNode.labelOffsetX = originalOffsetX + offset.x
            importantNode.labelOffsetY = originalOffsetY + offset.y
            let stillCollides = false
            for (const otherNode of this.mapData) {
              if (importantNode === otherNode || !otherNode.showLabel) continue
              if (this.checkLabelCollision(importantNode, otherNode)) {
                stillCollides = true
                break
              }
            }
            if (!stillCollides) {
              break
            }// 找到一个不碰撞的微调位置
            else { // 恢复
              importantNode.labelOffsetX = originalOffsetX
              importantNode.labelOffsetY = originalOffsetY
            }
          }
        }
      }
    }
  },
  destroyed () {
    clearInterval(this.timer)
    clearInterval(this.timer2)
    this.timer = null
    this.timer2 = null
  }
}
</script>
<style>
/* 地图和容器样式 */
.mapWrapper {
  width: 1150px;
  margin: 0 auto;
  overflow: hidden;
}

.chinaMap {
  height: 950px;
  background-image: url("../static/screen1/china.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100%;
  min-width: 1100px;
  background-position-y: -75px;
}

/* 点位样式 */
.point {
  position: absolute;
  z-index: 1;
  cursor: pointer;
  background-size: contain !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  transform-origin: center center;
}

.point-hover {
  z-index: 100;
}

/*
 * 呼吸动画关键帧 - 按负载等级分组
 * 设计理念：
 * 1. 负载越高，呼吸频率越快，效果越强烈
 * 2. 使用 translate(-50%, -50%) 确保动画围绕图标中心进行
 * 3. 通过缩放和透明度变化模拟呼吸效果
 * 4. 白色光晕适配所有图标颜色
 */

/* 零负载动画 - 最慢最轻微的呼吸 */
@keyframes breathe-zero {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.4;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.6);
    opacity: 0.8;
  }
}

/* 低负载动画 */
@keyframes breathe-low {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.4;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.6);
    opacity: 0.8;
  }
}

/* 中负载动画 */
@keyframes breathe-medium {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.7);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.9);
    opacity: 0.8;
  }
}

/* 高负载动画 */
@keyframes breathe-high {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.7);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(2.1);
    opacity: 0.9;
  }
}

/* 极高负载动画 */
@keyframes breathe-extreme {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.7);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(2.2);
    opacity: 0.9;
  }
}

/* 最高负载动画 */
@keyframes breathe-maximum {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.6);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(2.5);
    opacity: 1;
  }
}

/* 呼吸动画类 - 按负载等级分组 */

/* 零负载 */
.point-animation.icon-breathe-zero {
  animation: breathe-zero 8s ease-in-out infinite;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.2) 30%, rgba(255, 255, 255, 0.1) 60%, transparent 100%);
}

/* 低负载 */
.point-animation.icon-breathe-low {
  animation: breathe-low 6.5s ease-in-out infinite;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.25) 30%, rgba(255, 255, 255, 0.1) 60%, transparent 100%);
}

/* 中负载 */
.point-animation.icon-breathe-medium {
  animation: breathe-medium 5s ease-in-out infinite;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.6) 0%, rgba(255, 255, 255, 0.3) 30%, rgba(255, 255, 255, 0.12) 60%, transparent 100%);
}

/* 高负载 */
.point-animation.icon-breathe-high {
  animation: breathe-high 4s ease-in-out infinite;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.35) 30%, rgba(255, 255, 255, 0.15) 60%, transparent 100%);
}

/* 极高负载 */
.point-animation.icon-breathe-extreme {
  animation: breathe-extreme 3s ease-in-out infinite;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 30%, rgba(255, 255, 255, 0.18) 60%, transparent 100%);
}

/* 最高负载 */
.point-animation.icon-breathe-maximum {
  animation: breathe-maximum 2.5s ease-in-out infinite;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.45) 30%, rgba(255, 255, 255, 0.2) 60%, transparent 100%);
}

/* 悬停时增强效果 */
.point-animation.point-hover.icon-breathe-zero,
.point-animation.point-hover.icon-breathe-low,
.point-animation.point-hover.icon-breathe-medium,
.point-animation.point-hover.icon-breathe-high,
.point-animation.point-hover.icon-breathe-extreme,
.point-animation.point-hover.icon-breathe-maximum {
  animation-duration: 0.8s;
  z-index: 100;
}

/* 单位指示器样式 */
.unitPos {
  position: absolute;
  top: 500px;
  left: 130px;
}

.taskNumTitle {
  color: #1890ff;
  font-size: 14px;
  font-weight: 800;
}

.topArg {
  background-image: url("../assets/top.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
  position: relative;
  transform: rotateX(180deg);
  top: 7px;
}

.bottomArg {
  background-image: url("../assets/bottom.svg");
  display: inline-block;
  width: 15px;
  height: 15px;
}

.top {
  position: relative;
  left: 8px;
}

.topNum {
  color: #fff;
  display: inline-block;
  position: absolute;
  left: 25px;
  top: 10px;
  font-size: 14px;
}

.bottom {
  position: relative;
  left: 9px;
  top: -2px;
}

.bottomNum {
  color: #fff;
  display: inline-block;
  position: absolute;
  top: 0px;
  left: 25px;
  font-size: 14px;
}

.simple-linear {
  background: linear-gradient(#cc6600, #66ffff);
  width: 12px;
  height: 150px;
}

/* 点位和标签层 */
.pointWrapper,
.centerLabelsWrapper,
.animationWrapper {
  position: relative;
  top: -942px;
  left: 8px;
}

/* 交互层 - 必须在动画层之上以接收鼠标事件 */
.pointWrapper {
  z-index: 3;
}

.centerLabelsWrapper {
  pointer-events: none;
  z-index: 5;
}

/*
 * 动画效果层 - 专门用于呼吸特效
 * 与交互层分离，确保不影响原有点击和悬停功能
 */
.animationWrapper {
  pointer-events: none;  /* 不响应鼠标事件 */
  z-index: 2;           /* 在地图之上，交互层之下 */
}

.point-animation {
  position: absolute;
  border-radius: 50%;                    /* 圆形光晕 */
  pointer-events: none;                  /* 不响应鼠标事件 */
  transform-origin: center center;       /* 确保缩放围绕中心进行 */
  will-change: transform, opacity;       /* 硬件加速优化 */
  backface-visibility: hidden;           /* 防止闪烁 */
}

/* 标签样式 */
.centerLabel {
  position: absolute;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.9);
  font-size: 10px;
  text-align: center;
  white-space: nowrap;
  pointer-events: none;
  transition: all 0.2s ease;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.7);
}

.label-important {
  color: #ffffff;
  font-weight: bold;
  font-size: 11px;
  z-index: 6;
}

.label-high-load {
  color: #ffffff;
  font-weight: bold;
}

.label-hover {
  color: #ffffff;
  font-weight: bold;
  transform: translateX(-50%) scale(1.1);
  z-index: 10;
}

/* 任务详情弹窗 */
.taskWrapper {
  width: 280px;
  height: auto;
  background-color: rgba(16, 16, 16, 0.85);
  color: rgba(16, 16, 16, 1);
  font-size: 18px !important;
  text-align: left;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.7);
  font-family: Roboto;
  border: 1px solid rgba(50, 145, 248, 1);
  border-radius: 4px;
  position: absolute;
  z-index: 100;
  font-weight: 800;
  backdrop-filter: blur(5px);
  overflow: hidden;
  transition: all 0.3s ease;
}

.taskHeader {
  padding: 8px 12px;
  background-color: rgba(50, 145, 248, 0.8);
  color: #ffffff;
  font-weight: bold;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.taskBody {
  padding: 10px 12px;
}

.taskBody > div {
  margin-bottom: 8px;
}

.taskTitle1 {
  line-height: 20px;
  height: 20px;
  color: rgba(213, 213, 213, 1);
  font-size: 14px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
}

.taskTitle2 {
  line-height: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  text-align: left;
  font-family: SourceHanSansSC-bold;
  font-weight: 800;
}

.taskTitle3 {
  line-height: 20px;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  text-align: left;
  font-family: SourceHanSansSC-bold;
  font-weight: 800;
}

/* 悬停标签样式 */
.hoverLabel {
  position: absolute;
  z-index: 200;
  pointer-events: none;
  transition: all 0.15s ease;
  min-width: 80px;
  max-width: 160px;
  opacity: 0;
  transform: translateY(5px);
}

.hoverLabel-active {
  opacity: 1;
  transform: translateY(0);
}

.hoverContent {
  background-color: rgba(0, 10, 20, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  padding: 4px 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
}

.hoverContent span {
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  display: block;
  white-space: nowrap;
}

/* 任务状态指示器样式 */
.loadIndicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}

.load-zero {
  background-color: #808080;
  box-shadow: 0 0 3px rgba(128, 128, 128, 0.5);
}

.load-low {
  background-color: #00ff00;
  box-shadow: 0 0 3px rgba(0, 255, 0, 0.5);
}

.load-medium {
  background-color: #ffff00;
  box-shadow: 0 0 3px rgba(255, 255, 0, 0.5);
}

.load-high {
  background-color: #ff6600;
  box-shadow: 0 0 3px rgba(255, 102, 0, 0.5);
}

.load-veryhigh {
  background-color: #ff0000;
  box-shadow: 0 0 3px rgba(255, 0, 0, 0.5);
  animation: pulse-red 1s ease-in-out infinite;
}

@keyframes pulse-red {
  0%, 100% {
    box-shadow: 0 0 3px rgba(255, 0, 0, 0.5);
  }
  50% {
    box-shadow: 0 0 8px rgba(255, 0, 0, 0.8);
  }
}

</style>
