import { ref, computed, watch, nextTick } from 'vue'

/**
 * 地图控制组合式函数
 * 负责地图状态管理、数据更新、交互控制等
 */
export function useMapControl () {
  // ============ 地图状态 ============
  const mapRef = ref(null)
  const mapData = ref([])
  const mapConfig = ref({
    points: [],
    links: [],
    centerCoordinates: []
  })

  // 地图显示状态
  const mapDisplayState = ref({
    show1: true, // 算力资源调度
    show2: false, // 超宽带光纤直连
    show3: false // 其他网络
  })

  // 地图提示数据
  const tipData = ref({})

  // 地图加载状态
  const mapLoadingState = ref({
    isLoading: false,
    loadError: null
  })

  // ============ 地图控制方法 ============

  /**
   * 切换网络类型显示
   * @param {string} networkType - 网络类型 ('computing', 'fiber', 'other')
   */
  const changeNetworkType = (networkType) => {
    // 重置所有显示状态
    mapDisplayState.value.show1 = false
    mapDisplayState.value.show2 = false
    mapDisplayState.value.show3 = false

    switch (networkType) {
      case 'computing':
        mapDisplayState.value.show1 = true
        break
      case 'fiber':
        mapDisplayState.value.show2 = true
        break
      case 'other':
        mapDisplayState.value.show3 = true
        break
    }

    // 通知地图组件更新显示
    updateMapDisplay()
  }

  /**
   * 更新地图数据
   * @param {Array} newData - 新的地图数据
   */
  const updateMapData = (newData) => {
    if (!newData) return

    mapData.value = newData

    // 处理地图点位数据
    const points = newData.map(item => ({
      name: item.name || item.InfoName,
      coordinates: [item.coordinateX, item.coordinateY],
      province: item.province,
      computeScale: item.computeScale,
      connectionState: item.connectionState,
      centerType: item.centerType,
      // 添加更多需要显示的数据
      jobCount: item.jobCount,
      cardRunTime: item.cardRunTime,
      accessTime: item.accessTime
    }))

    mapConfig.value.points = points

    // 更新地图显示
    updateMapDisplay()
  }

  /**
   * 更新地图显示
   */
  const updateMapDisplay = async () => {
    await nextTick()

    if (mapRef.value && typeof mapRef.value.updateMapData === 'function') {
      mapRef.value.updateMapData(mapConfig.value)
    }
  }

  /**
   * 初始化地图演示数据
   */
  const initMapDemoData = () => {
    if (mapRef.value && typeof mapRef.value.initDemoData === 'function') {
      mapRef.value.initDemoData()
      mapRef.value.showDot()
    }
  }

  /**
   * 设置地图提示信息
   * @param {Object} data - 提示数据
   */
  const setTipData = (data) => {
    tipData.value = data
  }

  /**
   * 获取地图中心坐标
   * @param {Array} centerInfoList - 中心信息列表
   * @returns {Array} 中心坐标数组
   */
  const extractCenterCoordinates = (centerInfoList) => {
    if (!centerInfoList || !Array.isArray(centerInfoList)) {
      return []
    }

    return centerInfoList.map(center => ({
      name: center.InfoName || center.name,
      coordinates: [center.coordinateX, center.coordinateY],
      centerType: center.centerType || 'default'
    }))
  }

  /**
   * 处理地图点击事件
   * @param {Object} pointData - 点击的点位数据
   */
  const handleMapPointClick = (pointData) => {
    console.log('地图点位被点击:', pointData)
    setTipData(pointData)
    // 这里可以添加更多点击处理逻辑，比如显示详情弹窗
  }

  /**
   * 处理地图悬停事件
   * @param {Object} pointData - 悬停的点位数据
   */
  const handleMapPointHover = (pointData) => {
    setTipData(pointData)
  }

  /**
   * 重置地图状态
   */
  const resetMapState = () => {
    mapData.value = []
    mapConfig.value = {
      points: [],
      links: [],
      centerCoordinates: []
    }
    tipData.value = {}
    mapLoadingState.value = {
      isLoading: false,
      loadError: null
    }
  }

  /**
   * 设置地图加载状态
   * @param {boolean} loading - 是否加载中
   * @param {string|null} error - 错误信息
   */
  const setMapLoadingState = (loading, error = null) => {
    mapLoadingState.value.isLoading = loading
    mapLoadingState.value.loadError = error
  }

  // ============ 计算属性 ============
  const currentNetworkType = computed(() => {
    if (mapDisplayState.value.show1) return 'computing'
    if (mapDisplayState.value.show2) return 'fiber'
    if (mapDisplayState.value.show3) return 'other'
    return 'computing' // 默认值
  })

  const hasMapData = computed(() => {
    return mapData.value && mapData.value.length > 0
  })

  const mapPoints = computed(() => {
    return mapConfig.value.points || []
  })

  // ============ 监听器 ============
  watch(
    () => mapData.value,
    (newData) => {
      if (newData && newData.length > 0) {
        const coordinates = extractCenterCoordinates(newData)
        mapConfig.value.centerCoordinates = coordinates
      }
    },
    { deep: true }
  )

  // ============ 生命周期相关 ============
  const initializeMap = () => {
    setMapLoadingState(true)

    try {
      // 初始化地图默认状态
      changeNetworkType('computing')
      initMapDemoData()

      setMapLoadingState(false)
    } catch (error) {
      console.error('地图初始化失败:', error)
      setMapLoadingState(false, error.message)
    }
  }

  const cleanupMap = () => {
    resetMapState()

    if (mapRef.value && typeof mapRef.value.cleanup === 'function') {
      mapRef.value.cleanup()
    }
  }

  return {
    // 状态
    mapRef,
    mapData,
    mapConfig,
    mapDisplayState,
    tipData,
    mapLoadingState,

    // 计算属性
    currentNetworkType,
    hasMapData,
    mapPoints,

    // 方法
    changeNetworkType,
    updateMapData,
    updateMapDisplay,
    initMapDemoData,
    setTipData,
    extractCenterCoordinates,
    handleMapPointClick,
    handleMapPointHover,
    resetMapState,
    setMapLoadingState,
    initializeMap,
    cleanupMap
  }
}
