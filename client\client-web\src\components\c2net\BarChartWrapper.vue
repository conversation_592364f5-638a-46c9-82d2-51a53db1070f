<template>
  <div ref="chartContainer" :style="{ width: '100%', height: height + 'px' }"></div>
</template>

<script>
/**
 * 柱状图封装组件 - 封装柱状图的绘制逻辑
 */
export default {
  name: 'BarChartWrapper',
  props: {
    /**
     * 图表数据
     */
    chartData: {
      type: Object,
      required: true,
      default: () => ({
        xData: [],
        used: [],
        used2: []
      })
    },
    /**
     * 图表高度
     */
    height: {
      type: Number,
      default: 700
    },
    /**
     * 图表类型
     * 1: 提交任务量
     * 2: 任务平均等待时长
     * 3: 资源利用率对比
     */
    chartType: {
      type: Number,
      default: 1,
      validator: value => [1, 2, 3].includes(value)
    },
    /**
     * 任务ID和策略
     */
    taskInfo: {
      type: Object,
      default: () => ({
        taskId: 0,
        compareId: 0,
        strategy1: '',
        strategy2: ''
      })
    }
  },
  data () {
    return {
      myChart: null
    }
  },
  computed: {
    /**
     * 统计名称映射
     */
    nameMap () {
      return {
        1: '提交任务量',
        2: '任务平均等待时长',
        3: '资源利用率对比'
      }
    },
    /**
     * 当前图表名称
     */
    chartName () {
      return this.nameMap[this.chartType] || '提交任务量'
    }
  },
  watch: {
    chartData: {
      handler () {
        this.drawChart()
      },
      deep: true
    },
    chartType () {
      this.drawChart()
    }
  },
  mounted () {
    this.initChart()
    window.addEventListener('resize', this.resize)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.resize)
    this.myChart && this.myChart.dispose()
    this.myChart = null
  },
  methods: {
    /**
     * 初始化图表
     */
    initChart () {
      if (this.myChart) {
        this.myChart.dispose()
      }
      this.myChart = this.$echarts.init(this.$refs.chartContainer)
      this.drawChart()
    },

    /**
     * 绘制柱状图
     */
    drawChart () {
      const option = this.createBarChartOption()
      this.myChart && this.myChart.setOption(option, true)
    },

    /**
     * 创建柱状图配置
     */
    createBarChartOption () {
      // 确保即使没有数据也显示图表框架
      const hasData = this.chartData.xData && this.chartData.xData.length > 0

      const option = {
        title: {
          text: hasData ? '' : '暂无数据',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          right: '40%',
          top: '45%',
          padding: [10, 0, 0, 10]
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
            show: false
          },
          backgroundColor: '#000033',
          textStyle: { color: '#fff' },
          borderWidth: 0
        },
        legend: {
          show: true,
          top: 10,
          left: 50,
          itemWidth: 25,
          textStyle: { color: '#fff' },
          selectedMode: false
        },
        grid: {
          left: '45%',
          right: '20%',
          top: '10%'
        },
        yAxis: {
          type: 'category',
          nameTextStyle: {
            color: '#FFF',
            padding: [0, 0, 0, 0]
          },
          data: this.chartData.xData || [],
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            interval: 0,
            textStyle: {
              fontSize: 14,
              lineHeight: 12,
              fontWeight: 'bold',
              color: '#fff'
            },
            formatter: function (value, index) {
              if (value.length > 18) {
                return value.substr(0, 18) + '...'
              } else {
                return value
              }
            }
          },
          axisTick: {
            alignWithLabel: true,
            show: false
          }
        },
        xAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              width: 1,
              type: 'dotted',
              color: 'rgba(1, 145, 255, 0.3)'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: false,
            textStyle: {
              fontSize: '12',
              fontWeight: 'bold',
              color: '#fff'
            }
          },
          scale: true,
          min: 0,
          splitNumber: 4
        },
        series: this.createBarSeriesConfig(),
        animation: true,
        animationDuration: function (idx) {
          return idx * 100
        },
        animationEasing: 'backln'
      }

      if (this.taskInfo.compareId == 0) {
        option.series.length = 1
      }

      return option
    },

    /**
     * 创建柱状图系列配置
     */
    createBarSeriesConfig () {
      const formatter = (param) => {
        if (this.chartType == 1) {
          return param.data || 0
        } else if (this.chartType == 2) {
          return this.formatDuring2(param.data || 0)
        } else {
          return (param.data || 0).toFixed(2) + '%'
        }
      }

      return [
        {
          name: this.taskInfo.taskId == 0 ? '' : this.taskInfo.taskId + ' ' + this.taskInfo.strategy1,
          type: 'bar',
          barWidth: 10,
          z: 1,
          label: {
            show: true,
            position: 'right',
            textStyle: {
              color: '#fff'
            },
            formatter
          },
          emphasis: {
            focus: 'series'
          },
          data: this.chartData.used || [],
          itemStyle: {
            color: '#66ffff'
          }
        },
        {
          name: this.taskInfo.compareId == 0 ? '' : this.taskInfo.compareId + ' ' + this.taskInfo.strategy2,
          type: 'bar',
          barWidth: this.taskInfo.compareId == '0' ? 0 : 10,
          z: 2,
          label: {
            show: true,
            position: 'right',
            textStyle: {
              color: '#fff'
            },
            formatter
          },
          emphasis: {
            focus: 'series'
          },
          data: this.taskInfo.compareId != 0 ? this.chartData.used2 : [],
          itemStyle: {
            color: '#cc6600'
          }
        }
      ]
    },

    /**
     * 将秒转换为小时格式
     * @param {number} val 秒数
     * @returns {number} 小时数
     */
    formatDuring2 (val) {
      // 确保只返回小时数
      var hours = Math.floor(val * 3600 / 3600)
      return hours
    },

    /**
     * 调整图表大小
     */
    resize () {
      this.myChart && this.myChart.resize()
    }
  }
}
</script>
