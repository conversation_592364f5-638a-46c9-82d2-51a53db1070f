<template>
  <div class="main-container">
    <div ref="contentRef" class="content">
      <div class="top">
        <div class="title">智能联合调度</div>
      </div>
      <div class="center">
        <div class="title">枢纽调度中心</div>
      </div>
      <div>
        <div class="circle-1"></div>
        <div class="circle-2"></div>
        <div class="circle-3"></div>
        <div class="circle-4"></div>
        <div class="circle-5"></div>
        <div class="circle-6"></div>
      </div>
      <div>
        <div class="dot dot-1"></div>
        <div class="dot dot-2"></div>
        <div class="dot dot-3"></div>
        <div class="dot dot-4"></div>
        <div class="dot dot-5"></div>
        <div class="dot dot-6"></div>
      </div>
      <div>
        <div class="step step-1">
          <div class="step-content" :class="{
            active: curStep >= 0,
            isRunning: stepInfo[0].isRunning,
          }">
            <span v-show="curStep >= 0">{{ stepInfo[0].after + stepInfo[0].msg }}</span>
            <span v-show="curStep < 0">{{ stepInfo[0].before }}</span>
          </div>
        </div>
        <div class="step step-2">
          <div class="step-content" :class="{
            active: curStep >= 1,
            isRunning: stepInfo[1].isRunning,
          }">
            <span v-if="curStep >= 1">{{ stepInfo[1].after + stepInfo[1].msg }}</span>
            <span v-else="">{{ stepInfo[1].before }}</span>
          </div>
        </div>
        <div class="step step-3">
          <div class="step-content" :class="{
            active: curStep >= 2,
            isRunning: stepInfo[2].isRunning,
          }">
            <span v-if="curStep >= 2">{{ stepInfo[2].after + stepInfo[2].msg }}</span>
            <span v-else="">{{ stepInfo[2].before }}</span>
          </div>
        </div>
        <div class="step step-4">
          <div class="step-content" :class="{
            active: curStep >= 3,
            isRunning: stepInfo[3].isRunning,
          }">
            <span v-if="curStep >= 3">{{ stepInfo[3].after + stepInfo[3].msg }}</span>
            <span v-else="">{{ stepInfo[3].before }}</span>
          </div>
        </div>
        <div class="step step-5">
          <div class="step-content none" :class="curStep >= 4 ? 'active' : ''">
            <span v-if="curStep >= 4">{{ stepInfo[4].after }}</span>
            <span v-else="">{{ stepInfo[4].before }}</span>
          </div>
        </div>
        <div class="step step-6">
          <div class="step-content none" :class="curStep >= 5 ? 'active' : ''">
            <span v-if="curStep >= 5">{{ stepInfo[5].after }}</span>
            <span v-else="">{{ stepInfo[5].before }}</span>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div ref="aiCentersRef" class="ai-centers" :style="`width:${pW}px;height:${pH}px;left:${(2400 - pW) / 2}px;`">
          <div :class="{
            'ai-center': true,
            [`ai-center-${item.id}`]: true,
            'active': item.active,
            'blink-1': item.blinking1,
            'blink-2': item.blinking2
          }" :style="`width:${iW}px;height:${iH}px;`" v-for="(item, index) in aiCenters" :key="item.centerId">
            <div class="name"> {{ item.centerName }}</div>
            <div class="num"> {{ item.active && item.score >= 0 ? item.score : '' }}</div>
          </div>
        </div>
        <div class="selected-aiCenter" v-if="finished"><span>任务被调度到：{{ selectedAiCenter }}</span></div>
      </div>
      <div class="arr-1"></div>
      <div class="arr-2-c" v-if="finished">
        <div class="arr-2"></div>
      </div>
      <div class="task-list">
        <div class="title">任务调度回放</div>
        <div class="task" :class="curTask == item.jobId ? 'cur' : ''" v-for="(item, index) in taskList"
          @click="changeTask(item)">
          <div class="name">{{ item.jobName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getScheduleShowList, getScheduleRecords } from '@/api/screenService.js'
import anime from 'animejs/lib/anime.es.js'

export default {
  name: 'IntelligentJointScheduling',
  components: {},
  data () {
    return {
      pW: 850 * 2.45,
      pH: 250 * 2.45,
      iW: 267 * 0.85,
      iH: 301 * 0.85,
      offsetDeg: 0,
      aiCenters: [],
      curTask: 0,
      taskList: [],
      curStep: -1,
      stepInfo: [
        { before: '按用户请求中心', after: '用户请求中心：', msg: '', isRunning: false },
        { before: '按计算资源类型', after: '计算资源类型：', msg: '', isRunning: false },
        { before: '按资源规格', after: '资源规格：', msg: '', isRunning: false },
        { before: '按镜像', after: '镜像：', msg: '', isRunning: false },
        { before: '按忙闲程度', after: '按忙闲程度', msg: '', isRunning: false },
        { before: '按数据亲和', after: '按数据亲和', msg: '', isRunning: false }
      ],
      scheduleInfos: [],
      selectedAiCenter: '',
      timer: null,
      animeHandlers: [],
      loopAnimeHandlers: [],
      dotAnimeHandlers: [],
      finished: false,
      finishedIndex: 0
    }
  },
  methods: {
    initDot () {
      const dots = [
        { el: '.dot-1', w: 1596, h: 229 },
        { el: '.dot-2', w: 1521, h: 244 },
        { el: '.dot-3', w: 1438, h: 227 },
        { el: '.dot-4', w: 1356, h: 235 },
        { el: '.dot-5', w: 1277, h: 219 },
        { el: '.dot-6', w: 1200, h: 230 }
      ]
      for (let j = 0, jLen = dots.length; j < jLen; j++) {
        const dot = dots[j]
        const keyframes = []
        const offset = j / dots.length * Math.PI * 3 / 2
        for (let i = 0; i <= 100; i++) {
          const deg = Math.PI / 2 + Math.PI * 2 / 100 * i - offset
          const x = dot.w / 2 * Math.cos(deg)
          const y = dot.h / 2 * Math.sin(deg) - dot.h / 2
          const o = 1 * (Math.sin(deg) + 1) / 2
          keyframes.push({
            translateX: x,
            translateY: y,
            opacity: o
          })
        }
        const animeHandler1 = anime({
          targets: dot.el,
          ...keyframes[0],
          duration: 0
        })
        this.dotAnimeHandlers.push(animeHandler1)
        const animeHandler2 = anime({
          targets: dot.el,
          keyframes: keyframes,
          duration: 8000,
          easing: 'linear',
          loop: true
        })
        this.dotAnimeHandlers.push(animeHandler2)
      }
    },
    changeTask (item) {
      this.curTask = item.jobId
      getScheduleRecords({ jobId: this.curTask }).then(res => {
        const steps = res.scheduleInfos || []
        const step0 = steps[0]
        this.scheduleInfos = steps
        if (step0) {
          const aiCenters = step0.centerInfos || []
          this.aiCenters = aiCenters.map((item, index) => {
            const deg = (Math.PI / 2 + index * (2 * Math.PI / aiCenters.length)) % (2 * Math.PI)
            return {
              id: index,
              deg: deg,
              ...item,
              active: true,
              score: -1,
              blinking1: false,
              blinking2: false
            }
          })
          this.$nextTick(() => {
            this.initRender(0 / this.aiCenters.length * 2 * Math.PI)
            // this.renderLoop(0 / this.aiCenters.length * 2 * Math.PI);
            this.stepStart()
          })
        }
      }).catch(err => {
        console.log(err)
      })
    },
    stepStart () {
      if (this.finished) {
        // this.renderLoop(this.finishedIndex / this.aiCenters.length * 2 * Math.PI);
      }
      this.stepInfo.map(item => {
        item.isRunning = false
        item.msg = ''
      })
      this.curStep = -1
      this.finished = false
      this.selectedAiCenter = ''
      this.aiCenters.map(item => {
        item.score = -1
        item.active = true
      })
      this.nextStep()
    },
    nextStep () {
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.curStep = this.curStep + 1
        if (this.curStep >= 6) {
          this.finished = true
          const finalCentersInfo = this.scheduleInfos[this.curStep].centerInfos || []
          if (finalCentersInfo.length) {
            const id = finalCentersInfo[0].centerId
            let index = 0
            this.aiCenters.map((item, _index) => {
              if (item.centerId == id) {
                item.score = finalCentersInfo[0].score
                index = _index
              } else {
                if (item.active) {
                  item.active = false
                  item.blinking1 = true
                  setTimeout(() => {
                    item.blinking1 = false
                  }, 1600)
                }
              }
            })
            this.finishedIndex = index
            this.selectedAiCenter = this.aiCenters[index].centerName
            this.renderLoop(index / this.aiCenters.length * 2 * Math.PI)
            this.setCurrent(index)
          }
          this.timer = setTimeout(() => {
            // this.renderLoop(this.finishedIndex / this.aiCenters.length * 2 * Math.PI);
            this.stepStart()
          }, 10000)
          return
        }
        const centersInfo = this.scheduleInfos[this.curStep].centerInfos || []
        const type = this.scheduleInfos[this.curStep].type
        const msg = this.scheduleInfos[this.curStep].message
        this.stepInfo[this.curStep].msg = msg
        this.stepInfo[this.curStep].isRunning = true
        setTimeout(() => {
          if (this.stepInfo[this.curStep]) {
            this.stepInfo[this.curStep].isRunning = false
          }
        }, 5700)
        this.aiCenters.map((item1) => {
          centersInfo.map(item2 => {
            if (item1.centerId == item2.centerId) {
              item1.score = item2.score
              if (type == 'filter' && item1.score <= 0) {
                item1.active = false
              }
              if (type == 'filter' && item1.score > 0) {
                item1.active = true
                item1.blinking2 = true
                setTimeout(() => {
                  item1.blinking2 = false
                }, 1600)
              }
              if (type == 'score') {
                item1.blinking2 = true
                setTimeout(() => {
                  item1.blinking2 = false
                }, 1600)
              }
            }
          })
        })
        this.nextStep()
      }, this.curStep < 0 ? 1500 : 6500)
    },
    initData () {
      getScheduleShowList().then(res => {
        this.taskList = res.jobInfos || []
        if (this.taskList.length) {
          this.changeTask(this.taskList[0])
        }
      }).catch(err => {
        console.log(err)
      })
    },
    initRender (offsetDeg) {
      this.removeAnimations()
      offsetDeg = offsetDeg || 0
      this.offsetDeg = offsetDeg
      this.aiCenters.map((item, index) => {
        const deg = (Math.PI / 2 + offsetDeg + index * (2 * Math.PI / this.aiCenters.length)) % (2 * Math.PI)
        const zIndex = Math.ceil(Math.cos(deg - Math.PI / 2) * 100) + 200
        const x = this.pW / 2 - this.iW / 2 + this.pW / 2 * Math.cos(deg)
        const y = this.pH / 2 - this.iH / 1.4 + this.pH / 2 * Math.sin(deg)
        const s = 1 - (200 - zIndex) / 200 * 0.2
        const animeHandler = anime({
          targets: `.ai-center-${item.id}`,
          translateX: x,
          translateY: y,
          zIndex: zIndex,
          scale: s,
          duration: 0
        })
        this.animeHandlers.push(animeHandler)
      })
    },
    renderLoop (offsetDeg) {
      this.removeAnimations()
      offsetDeg = offsetDeg || 0
      this.aiCenters.map((item, index) => {
        const keyframes = []
        for (let i = 0; i <= 100; i++) {
          const deg = (item.deg - offsetDeg + Math.PI * 2 / 100 * i) % (2 * Math.PI)
          const zIndex = Math.ceil(Math.cos(deg - Math.PI / 2) * 100) + 200
          const x = this.pW / 2 - this.iW / 2 + this.pW / 2 * Math.cos(deg)
          const y = this.pH / 2 - this.iH / 1.4 + this.pH / 2 * Math.sin(deg)
          const s = 1 - (200 - zIndex) / 200 * 0.2
          keyframes.push({
            translateX: x,
            translateY: y,
            zIndex: zIndex,
            scale: s
          })
        }
        const animeHandler1 = anime({
          targets: `.ai-center-${item.id}`,
          ...keyframes[0],
          duration: 0
        })
        this.animeHandlers.push(animeHandler1)
        const animeHandler2 = anime({
          targets: `.ai-center-${item.id}`,
          keyframes: keyframes,
          easing: 'linear',
          duration: 25000,
          loop: true,
          update: (_anime) => {
            const index = Math.ceil(_anime.currentTime / _anime.duration * (keyframes.length - 1))
            const zIndex = keyframes[index].zIndex
            if (document.querySelector(`.ai-center-${item.id}`)) {
              document.querySelector(`.ai-center-${item.id}`).style.zIndex = zIndex
            }
          }
        })
        this.loopAnimeHandlers.push(animeHandler2)
        return item
      })
    },
    removeAnimations () {
      this.animeHandlers.map(animeHandler => {
        animeHandler.reset()
        animeHandler.remove()
      })
      this.animeHandlers = []
      this.loopAnimeHandlers.map(animeHandler => {
        animeHandler.reset()
        animeHandler.remove()
      })
      this.loopAnimeHandlers = []
      this.aiCenters.map(item => {
        anime.remove(`.ai-center-${item.id}`)
      })
    },
    setCurrent (itemIndex) {
      const animeHandler = this.loopAnimeHandlers[itemIndex]
      this.removeAnimations()
      if (!animeHandler) {
        this.initRender(itemIndex / this.aiCenters.length * 2 * Math.PI)
        return
      }
      const useTime = animeHandler.currentTime / animeHandler.duration
      const offset = this.aiCenters[itemIndex].deg - Math.PI / 2
      this.aiCenters.map((item, index) => {
        const keyframes = []
        for (let i = 0; i <= 100; i++) {
          const deg = (item.deg + 2 * Math.PI * useTime + (2 * Math.PI * (1 - useTime) - offset) / 100 * i) % (2 * Math.PI)
          const zIndex = Math.ceil(Math.cos(deg - Math.PI / 2) * 100) + 200
          const x = this.pW / 2 - this.iW / 2 + this.pW / 2 * Math.cos(deg)
          const y = this.pH / 2 - this.iH / 1.4 + this.pH / 2 * Math.sin(deg)
          const s = 1 - (200 - zIndex) / 200 * 0.2
          keyframes.push({
            translateX: x,
            translateY: y,
            zIndex: zIndex,
            scale: s
          })
        }
        const animeHandler = anime({
          targets: `.ai-center-${item.id}`,
          keyframes: keyframes,
          duration: 1500,
          easing: 'linear',
          loop: 1
        })
        this.animeHandlers.push(animeHandler)
        return item
      })
    },
    resize () {
      const winW = document.documentElement.clientWidth
      const winH = document.documentElement.clientHeight
      const scale = Math.min(winW / 2304, winH / 2304)
      this.$refs.contentRef.style.scale = scale
    }
  },
  beforeMount () {
  },
  mounted () {
    window.anime = anime
    this.resize()
    window.addEventListener('resize', this.resize)
    this.initDot()
    this.initData()
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.resize)
    this.timer && clearInterval(this.timer)
    this.removeAnimations()
    this.dotAnimeHandlers.map(animeHandler => {
      animeHandler.reset()
      animeHandler.remove()
    })
  }
}
</script>

<style scoped lang="less">
.main-container {
  height: 100Vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
  background: radial-gradient(95.19% 95.19% at 50% 81%, rgb(0, 24, 118), rgb(26, 0, 59) 100%);

  .content {
    position: absolute;
    user-select: none;
    left: 0;
    top: 0;
    height: 2304px;
    width: 2304px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    transform-origin: 0 0;
    font-family: 思源黑体;

    .top {
      position: absolute;
      width: 2286px;
      height: 177px;
      background-image: url('../assets/screen-2/dd_top_bg.png');
      background-size: cover;
      top: 64px;
      left: 5px;
      display: flex;
      align-items: center;
      justify-content: center;

      .title {
        background: linear-gradient(180.00deg, rgb(255, 255, 255), rgb(207, 242, 255));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 80px;
        font-weight: 900;
        letter-spacing: 15px;
        padding-left: 20px;
      }
    }

    .center {
      position: absolute;
      width: 1665px;
      height: 1123px;
      left: 320px;
      top: 249px;
      background-image: url('../assets/screen-2/dd_center_bg.png');
      background-size: cover;

      .title {
        position: absolute;
        width: 100%;
        height: 66px;
        top: 328px;
        color: rgb(255, 255, 255);
        font-family: 思源黑体;
        font-size: 36px;
        font-weight: 700;
        line-height: 50px;
        letter-spacing: 0px;
        text-align: center;
      }
    }

    .circle-1 {
      position: absolute;
      width: 1596px;
      height: 229px;
      left: 354px;
      top: 576px;
      border-radius: 50%;
      background: url('../assets/screen-2/elliptic-1.png') center center no-repeat;
    }

    .circle-2 {
      position: absolute;
      width: 1521px;
      height: 244px;
      left: 392px;
      top: 679px;
      border-radius: 50%;
      background: url('../assets/screen-2/elliptic-2.png') center center no-repeat;
    }

    .circle-3 {
      position: absolute;
      width: 1438px;
      height: 227px;
      left: 433px;
      top: 797px;
      border-radius: 50%;
      background: url('../assets/screen-2/elliptic-3.png') center center no-repeat;
    }

    .circle-4 {
      position: absolute;
      width: 1356px;
      height: 235px;
      left: 474px;
      top: 898px;
      border-radius: 50%;
      background: url('../assets/screen-2/elliptic-4.png') center center no-repeat;
    }

    .circle-5 {
      position: absolute;
      width: 1277px;
      height: 219px;
      left: 514px;
      top: 1024px;
      border-radius: 50%;
      background: url('../assets/screen-2/elliptic-5.png') center center no-repeat;
    }

    .circle-6 {
      position: absolute;
      width: 1200px;
      height: 230px;
      left: 552px;
      top: 1116px;
      border-radius: 50%;
      background: url('../assets/screen-2/elliptic-6.png') center center no-repeat;
    }

    .bottom {
      position: absolute;
      width: 2400px;
      height: 1241px;
      left: -48px;
      top: 1380px;
      background-image: url('../assets/screen-2/dd_bottom_bg.png');
      background-size: cover;

      .ai-centers {
        position: absolute;
        top: 120px;
        left: 179px;
        width: 1786px;
        height: 496px;
        border-radius: 50%;

        :deep(.ai-center) {
          position: absolute;
          width: 250px;
          height: 250px;
          font-size: 50px;
          text-align: center;
          color: white;
          background-image: url('../assets/screen-2/dd_IDC-off.png');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: bottom;
          opacity: 0.55;

          .name {
            position: absolute;
            bottom: -25px;
            width: 300%;
            left: -100%;
            font-size: 26px;
            text-align: center;
          }

          .num {
            position: absolute;
            width: 100%;
            top: 40%;
            color: rgb(255, 199, 0);
            font-family: 思源黑体;
            font-size: 38px;
            font-weight: 700;
            text-align: center;
          }

          &.active {
            background-image: url('../assets/screen-2/dd_IDC-active.png');
            opacity: 1;
          }

          &.blink-1 {
            animation: blink-1 1.5s ease;
          }

          &.blink-2 {
            animation: blink-2 1.5s ease;
          }
        }
      }

      .selected-aiCenter {
        position: absolute;
        left: 5%;
        top: 348px;
        width: 90%;
        text-align: center;
        color: rgba(170, 205, 255, 0.4);
        font-family: 思源黑体;
        font-size: 48px;
        font-weight: 700;

        span {
          background: linear-gradient(180.00deg, rgb(154, 226, 255), rgb(0, 183, 255));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
          animation: blink-2 1.5s ease;
        }
      }
    }

    .task-list {
      position: absolute;
      right: 40px;
      top: 960px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .title {
        color: rgb(50, 197, 255);
        font-family: 思源黑体;
        font-size: 36px;
        font-weight: 900;
        letter-spacing: 5px;
        margin-bottom: 40px;
      }

      .task {
        width: 420px;
        height: 50px;
        background: linear-gradient(270.00deg, rgb(1, 145, 255), rgb(26, 28, 150) 100%);
        border-radius: 30px;
        display: flex;
        margin-bottom: 36px;
        cursor: pointer;
        transition: all 200ms;

        .name {
          width: 100%;
          display: flex;
          align-items: center;
          padding-left: 30px;
          color: rgba(255, 255, 255, 0.6);
          font-family: 思源黑体;
          font-size: 24px;
          font-weight: 500;
        }

        &.cur {
          width: 470px;
          background: linear-gradient(270.00deg, rgb(1, 212, 23), rgb(26, 28, 150) 100%);
        }
      }
    }

    .step {
      position: absolute;
      width: 742px;
      height: 61px;
      color: rgba(170, 205, 255, 0.4);
      font-family: 思源黑体;
      font-size: 48px;
      font-weight: 700;
      text-align: center;
      left: 782px;
      transition: all 200ms;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      border-radius: 16px;

      &.step-1 {
        top: 713px;
      }

      &.step-2 {
        top: 828px;
      }

      &.step-3 {
        top: 940px;
      }

      &.step-4 {
        top: 1045px;
      }

      &.step-5 {
        top: 1152px;
      }

      &.step-6 {
        top: 1260px;
      }
    }

    .step-content {
      display: inline;

      span {
        display: inline;
        padding-left: 20px;
        padding-right: 20px;
      }

      &.active {
        span {
          background: linear-gradient(180.00deg, rgb(154, 226, 255), rgb(0, 183, 255));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
          animation: blink-2 1.5s ease;
        }
      }

      &.isRunning {
        animation: text-scroll linear 5.5s 1;
        animation-delay: 100ms;
        display: inline-block;

        span {
          display: inline-block;
        }
      }

      &.none {
        animation: none;
      }
    }

    .arr-1 {
      position: absolute;
      height: 68px;
      width: 80px;
      background: url('../assets/screen-2/arr-1.png') center center no-repeat;
      top: 640px;
      left: 1116px;
      animation: move-up-down 2.5s ease infinite;
    }

    .arr-2-c {
      animation: show-in 2s;
    }

    .arr-2 {
      position: absolute;
      height: 476px;
      width: 80px;
      background: url('../assets/screen-2/arr-2.png') center center no-repeat;
      top: 1453px;
      left: 1116px;
      animation: move-up-down 2.5s ease infinite;
    }

    .dot {
      position: absolute;
      width: 20px;
      height: 20px;
      background: radial-gradient(50.00% 50.00% at 50% 50%, rgb(50, 197, 255), rgba(0, 105, 255, 0.35) 100%);
      border-radius: 50%;
      left: 1143px;
    }

    .dot-1 {
      top: 793px;
    }

    .dot-2 {
      top: 911px;
    }

    .dot-3 {
      top: 1011px;
    }

    .dot-4 {
      top: 1120px;
    }

    .dot-5 {
      top: 1230px;
    }

    .dot-6 {
      top: 1334px;
    }
  }
}

@keyframes move-up-down {
  0% {
    transform: translateY(-20px);
  }

  50% {
    transform: translateY(0px);
  }

  100% {
    transform: translateY(-20px);
  }
}

@keyframes blink-1 {
  0% {
    opacity: 1;
  }

  25% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  75% {
    opacity: 0;
  }

  100% {
    opacity: 0.55;
  }
}

@keyframes blink-2 {
  0% {
    opacity: 1;
  }

  25% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  75% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes show-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes text-scroll {
  // 0% {
  //   margin-left: 0;
  //   transform: translateX(0);
  // }

  // 10% {
  //   margin-left: 0;
  //   transform: translateX(0);
  // }

  // 90% {
  //   margin-left: 100%;
  //   transform: translateX(-100%);
  // }

  // 100% {
  //   margin-left: 100%;
  //   transform: translateX(-100%);
  // }
  0% {
    margin-left: 0;
    transform: translateX(0);
  }

  49.999% {
    transform: translateX(-100%);
  }

  50% {
    // transform: translateX(100%);
    transform: translateX(750px);
  }

  100% {
    transform: translateX(0);
  }
}
</style>
