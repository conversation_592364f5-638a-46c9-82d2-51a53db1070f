/**
 * 网络配置混入 - 封装网络配置相关操作逻辑
 */
export default {
  data () {
    return {
      // 网络配置的默认值
      networkConfig: {
        construct: false,
        accelerator: false,
        topologyTpye: 'topology01' // 默认选择拓扑01
      }
    }
  },
  methods: {
    /**
     * 初始化网络配置
     * @param {Object} config - 网络配置对象
     */
    initNetworkConfig (config = {}) {
      this.networkConfig = {
        ...this.networkConfig,
        ...config
      }
    },

    /**
     * 获取网络配置数据
     * @returns {Object} - 网络配置数据
     */
    getNetworkConfig () {
      return {
        construct: this.taskForm.construct,
        accelerator: this.taskForm.accelerator,
        topologyTpye: this.taskForm.topologyTpye
      }
    },

    /**
     * 验证网络配置
     * @returns {Boolean} - 验证结果
     */
    validateNetworkConfig () {
      if (this.taskForm.construct && !this.taskForm.topologyTpye) {
        this.$message.error('启用网络拓扑时必须选择拓扑类型')
        return false
      }
      return true
    }
  }
}
