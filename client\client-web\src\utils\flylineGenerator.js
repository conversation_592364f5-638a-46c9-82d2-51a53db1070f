/**
 * 飞线生成器 - 完全确定性的飞线生成逻辑
 * 基于SendToInfoMap数据生成飞线，无随机成分
 */
export class FlylineGenerator {
  constructor () {
    this.MAX_CONNECTIONS_PER_CENTER = 6
  }

  /**
   * 验证SendToInfoMap数据源的有效性
   * @param {Object} params 参数对象
   * @param {Object} params.taskResponse 任务响应数据
   * @param {Array} params.tempData 临时数据
   * @param {Array} params.mapData 地图数据
   * @param {number} params.count 当前帧数
   * @returns {boolean} 数据源是否有效
   */
  validateSendToInfoMapDataSource ({ taskResponse, tempData, mapData, count }) {
    // 检查是否有有效的数据源
    const hasTaskResponse = taskResponse &&
                           taskResponse.CenterInfoToWebList &&
                           taskResponse.CenterInfoToWebList.length > 0

    const hasTempData = tempData && tempData.length > 0
    const hasMapData = mapData && mapData.length > 0

    if (!hasTaskResponse && !hasTempData && !hasMapData) {
      return false
    }

    // 检查当前帧是否有效
    if (typeof count !== 'number' || count < 0) {
      return false
    }

    // 检查是否存在SendToInfoMap数据
    let hasSendToInfoMap = false

    if (hasTaskResponse) {
      for (const center of taskResponse.CenterInfoToWebList) {
        if (center.SnapshotInfoToWebList &&
            center.SnapshotInfoToWebList[count] &&
            center.SnapshotInfoToWebList[count].SendToInfoMap) {
          hasSendToInfoMap = true
          break
        }
      }
    }

    if (!hasSendToInfoMap && hasTempData) {
      for (const center of tempData) {
        if (center.SnapshotInfoToWebList &&
            center.SnapshotInfoToWebList[count] &&
            center.SnapshotInfoToWebList[count].SendToInfoMap) {
          hasSendToInfoMap = true
          break
        }
      }
    }

    if (!hasSendToInfoMap) {
      return false
    }

    return true
  }

  /**
   * 从当前帧数据中提取SendToInfoMap
   * @param {Object} params 参数对象
   * @param {Object} params.taskResponse 任务响应数据
   * @param {Array} params.tempData 临时数据
   * @param {Array} params.mapData 地图数据
   * @param {number} params.count 当前帧数
   * @returns {Array} 发送关系数组
   */
  extractSendToInfoMaps ({ taskResponse, tempData, mapData, count }) {
    const sendToInfoMaps = []

    try {
      // 优先从taskResponse中获取数据
      if (taskResponse && taskResponse.CenterInfoToWebList) {
        taskResponse.CenterInfoToWebList.forEach((centerData, centerIndex) => {
          if (!centerData.SnapshotInfoToWebList ||
              !centerData.SnapshotInfoToWebList[count]) {
            return
          }

          const currentSnapshot = centerData.SnapshotInfoToWebList[count]

          // 检查是否存在SendToInfoMap（对象格式）
          if (currentSnapshot.SendToInfoMap && typeof currentSnapshot.SendToInfoMap === 'object') {
            // 遍历SendToInfoMap对象的每个键值对
            Object.entries(currentSnapshot.SendToInfoMap).forEach(([targetId, sendAmount]) => {
              // 确保发送数量大于0才创建飞线
              if (sendAmount > 0) {
                sendToInfoMaps.push({
                  sourceName: centerData.InfoName || centerData.CenterName || `Center${centerIndex}`,
                  sourceIndex: centerIndex,
                  targetId: targetId,
                  targetIndex: parseInt(targetId),
                  sendAmount: sendAmount,
                  sendType: 'task'
                })
              }
            })
          }
        })
      }

      // 如果taskResponse没有数据，尝试从tempData获取
      if (sendToInfoMaps.length === 0 && tempData && tempData.length > 0) {
        tempData.forEach((centerData, centerIndex) => {
          if (!centerData.SnapshotInfoToWebList ||
              !centerData.SnapshotInfoToWebList[count]) {
            return
          }

          const currentSnapshot = centerData.SnapshotInfoToWebList[count]

          // 检查是否存在SendToInfoMap（对象格式）
          if (currentSnapshot.SendToInfoMap && typeof currentSnapshot.SendToInfoMap === 'object') {
            // 遍历SendToInfoMap对象的每个键值对
            Object.entries(currentSnapshot.SendToInfoMap).forEach(([targetId, sendAmount]) => {
              // 确保发送数量大于0才创建飞线
              if (sendAmount > 0) {
                sendToInfoMaps.push({
                  sourceName: centerData.name || centerData.InfoName || `Center${centerIndex}`,
                  sourceIndex: centerIndex,
                  targetId: targetId,
                  targetIndex: parseInt(targetId),
                  sendAmount: sendAmount,
                  sendType: 'task'
                })
              }
            })
          }
        })
      }

      // 如果还是没有数据，尝试从mapData获取
      if (sendToInfoMaps.length === 0) {
        mapData.forEach((centerData, centerIndex) => {
          // 如果mapData中有SendToInfoMap相关数据（对象格式）
          if (centerData.SendToInfoMap && typeof centerData.SendToInfoMap === 'object') {

            // 遍历SendToInfoMap对象的每个键值对
            Object.entries(centerData.SendToInfoMap).forEach(([targetId, sendAmount]) => {
              // 确保发送数量大于0才创建飞线
              if (sendAmount > 0) {
                sendToInfoMaps.push({
                  sourceName: centerData.name || `Center${centerIndex}`,
                  sourceIndex: centerIndex,
                  targetId: targetId,
                  targetIndex: parseInt(targetId),
                  sendAmount: sendAmount,
                  sendType: 'task'
                })
              }
            })
          }
        })
      }
    } catch (error) {
      // 提取SendToInfoMap时发生错误，返回空数组
    }

    return sendToInfoMaps
  }

  /**
   * 根据索引查找中心
   * @param {number} centerIndex 中心索引
   * @param {Array} mapData 地图数据
   * @returns {Object|null} 中心对象或null
   */
  findCenterByIndex (centerIndex, mapData) {
    // 首先尝试直接通过索引查找
    if (mapData && mapData[centerIndex]) {
      return mapData[centerIndex]
    }

    // 如果直接索引不存在，尝试通过名称匹配
    const centerName = `Center${centerIndex}`
    return mapData.find(center =>
      center.name === centerName ||
      center.InfoName === centerName ||
      center.apiName === centerName ||
      center.index === centerIndex
    )
  }

  /**
   * 计算飞线曲率（完全确定性，无随机成分）
   * @param {Object} sourceCenter 源中心
   * @param {Object} targetCenter 目标中心
   * @param {number} distance 距离
   * @param {number} sendAmount 发送数量
   * @returns {number} 曲率值
   */
  calculateCurvature (sourceCenter, targetCenter, distance, sendAmount) {
    // 计算相对位置决定曲率方向
    const centerX = 0.5
    const centerY = 0.5
    const sourceToCenterX = sourceCenter.x - centerX
    const sourceToCenterY = sourceCenter.y - centerY
    const targetToCenterX = targetCenter.x - centerX
    const targetToCenterY = targetCenter.y - centerY

    // 确定曲率方向
    const inOppositeQuadrants = (
      (sourceToCenterX * targetToCenterX < 0) ||
      (sourceToCenterY * targetToCenterY < 0)
    )

    // 根据位置优化曲率
    let curvature
    if (inOppositeQuadrants) {
      // 对角线方向的点使用较小曲率
      curvature = Math.max(0.1, Math.min(0.3, distance * 0.3))
    } else {
      // 同一象限或相邻象限的点使用较大曲率，避免重叠
      curvature = Math.max(0.3, Math.min(0.7, distance * 0.6))
    }

    // 使用确定性偏移避免重叠，基于源点和目标点的坐标计算
    const coordinateHash = (sourceCenter.x * 1000 + sourceCenter.y * 100 + targetCenter.x * 10 + targetCenter.y) % 100
    const deterministicOffset = (coordinateHash % 20) / 200 // 0-0.1的偏移范围

    // 根据发送数量微调曲率，数量越大曲率越明显
    const amountAdjustment = Math.min(0.1, sendAmount / 100)

    curvature += deterministicOffset + amountAdjustment

    return Math.max(0.05, Math.min(0.8, curvature))
  }

  /**
   * 根据发送信息创建飞线（完全确定性）
   * @param {Object} sourceCenter 源中心
   * @param {Object} targetCenter 目标中心
   * @param {Object} sendInfo 发送信息
   * @returns {Object} 飞线配置对象
   */
  createFlylineFromSendInfo (sourceCenter, targetCenter, sendInfo) {
    // 计算两点间的距离
    const dx = sourceCenter.x - targetCenter.x
    const dy = sourceCenter.y - targetCenter.y
    const distance = Math.sqrt(dx * dx + dy * dy)

    // 根据发送数量决定线条样式
    const sendAmount = sendInfo.sendAmount || 1
    const lineWidth = Math.max(2, Math.min(8, 3 + Math.log(sendAmount)))
    const particleNum = Math.max(1, Math.min(6, Math.ceil(sendAmount / 10)))

    // 根据发送数量决定颜色
    let lineColor
    if (sendAmount < 5) {
      lineColor = '#00a8ff' // 蓝色 - 少量发送
    } else if (sendAmount < 20) {
      lineColor = '#ffb700' // 橙色 - 中等发送
    } else {
      lineColor = '#ff8c00' // 红橙色 - 大量发送
    }

    // 计算动画速度
    const baseSpeed = 0.4
    const speedAdjustByDistance = Math.max(0.1, 0.5 - distance * 0.3)
    const speedAdjustByAmount = Math.max(0.1, Math.min(0.4, sendAmount / 50))
    const finalSpeed = baseSpeed + speedAdjustByDistance + speedAdjustByAmount

    // 计算动画周期
    const durationBase = Math.max(10, 35 - sendAmount)
    const duration = [durationBase * 0.9, durationBase * 1.1]

    // 计算曲率（基于发送数量的确定性计算）
    const curvature = this.calculateCurvature(sourceCenter, targetCenter, distance, sendAmount)

    return {
      source: sourceCenter.name,
      target: targetCenter.name,
      width: lineWidth,
      color: lineColor,
      orbitColor: 'rgba(105, 140, 188, 0.3)',
      duration: duration,
      radius: 2 + sendAmount / 20,
      k: finalSpeed,
      curvature: curvature,
      num: particleNum,
      // 添加发送信息到飞线数据中，便于调试
      sendAmount: sendAmount,
      sendType: sendInfo.sendType
    }
  }

  /**
   * 基于SendToInfoMap生成飞线（完全确定性，无随机成分）
   * @param {Object} params 参数对象
   * @param {Object} params.taskResponse 任务响应数据
   * @param {Array} params.tempData 临时数据
   * @param {Array} params.mapData 地图数据
   * @param {number} params.count 当前帧数
   * @returns {Array} 生成的飞线数组
   */
  generateFlylinesFromSendToInfoMap ({ taskResponse, tempData, mapData, count }) {
    // 严格验证数据来源
    if (!this.validateSendToInfoMapDataSource({ taskResponse, tempData, mapData, count })) {
      return []
    }

    // 获取当前帧的SendToInfoMap数据
    const sendToInfoMaps = this.extractSendToInfoMaps({ taskResponse, tempData, mapData, count })

    if (!sendToInfoMaps || sendToInfoMaps.length === 0) {
      return []
    }

    // 验证所有发送关系数据的有效性
    const validSendToInfoMaps = sendToInfoMaps.filter(sendInfo => {
      return sendInfo.sendAmount > 0 &&
             typeof sendInfo.sourceIndex === 'number' &&
             typeof sendInfo.targetIndex === 'number' &&
             sendInfo.sourceIndex !== sendInfo.targetIndex
    })

    if (validSendToInfoMaps.length === 0) {
      return []
    }

    // 追踪每个中心的连接数，避免过多连线
    const centerConnectionCount = {}
    mapData.forEach(item => {
      centerConnectionCount[item.name] = 0
    })

    // 根据SendToInfoMap创建飞线（按发送数量排序，优先显示重要连接）
    const sortedSendInfoMaps = validSendToInfoMaps.sort((a, b) => b.sendAmount - a.sendAmount)
    const generatedFlylines = []

    sortedSendInfoMaps.forEach((sendInfo) => {
      // 根据索引查找源中心和目标中心
      const sourceCenter = this.findCenterByIndex(sendInfo.sourceIndex, mapData)
      const targetCenter = this.findCenterByIndex(sendInfo.targetIndex, mapData)

      if (!sourceCenter || !targetCenter) {
        return
      }

      // 检查连线数量限制
      if (centerConnectionCount[sourceCenter.name] >= this.MAX_CONNECTIONS_PER_CENTER ||
          centerConnectionCount[targetCenter.name] >= this.MAX_CONNECTIONS_PER_CENTER) {
        return
      }

      // 增加连线计数
      centerConnectionCount[sourceCenter.name]++
      centerConnectionCount[targetCenter.name]++

      // 创建飞线（完全基于确定性数据）
      const flyline = this.createFlylineFromSendInfo(sourceCenter, targetCenter, sendInfo)
      generatedFlylines.push(flyline)
    })

    return generatedFlylines
  }

  /**
   * 打印数据结构用于分析（已禁用调试输出）
   * @param {Object} params 参数对象
   * @param {Object} params.taskResponse 任务响应数据
   * @param {Array} params.tempData 临时数据
   * @param {Array} params.mapData 地图数据
   * @param {number} params.count 当前帧数
   * @param {number} params.total 总帧数
   */
  logDataStructure ({ taskResponse, tempData, mapData, count, total }) {
    // 调试输出已禁用
  }
}

// 创建单例实例
export const flylineGenerator = new FlylineGenerator()

// 默认导出
export default flylineGenerator
