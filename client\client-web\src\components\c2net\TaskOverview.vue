<template>
  <div class="task-overview">
    <div class="showMt">
      <el-row class="linear-gradient" style="margin-bottom: 3px">
        <el-col :span="14">
          <el-row>
            <el-col :span="5">
              <div class="arrow"></div>
            </el-col>
            <el-col :span="19">
              <div class="title1">仿真任务总览</div>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <el-row justify="space-between" type="flex">
        <el-col :span="24">
          <div>
            <cumulative-task :Data="circleData" :msg="taskMsg"></cumulative-task>
          </div>
          <div>
            <line-chart-wrapper
              ref="lineChart"
              :chart-data="chartData"
              :height="chartHeight"
            ></line-chart-wrapper>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import CumulativeTask from '@/components/circle'
import LineChartWrapper from './LineChartWrapper.vue'

/**
 * 任务总览组件 - 包含累计任务环形图和折线图
 */
export default {
  name: 'TaskOverview',
  components: {
    CumulativeTask,
    LineChartWrapper
  },
  props: {
    /**
     * 任务基本信息
     */
    taskMsg: {
      type: Object,
      default: () => ({
        totalNum: 0,
        execNum: 0
      })
    },
    /**
     * 图表数据
     */
    chartData: {
      type: Object,
      default: () => ({
        xAxis: [],
        yAxis: []
      })
    },
    /**
     * 图表高度
     */
    chartHeight: {
      type: Number,
      default: 250
    }
  },
  data () {
    return {
      circleData: {
        xAxis: [],
        yAxis: []
      }
    }
  },
  watch: {
    chartData: {
      handler (newVal) {
        this.circleData = newVal
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /**
     * 重绘图表
     */
    redrawChart () {
      this.$refs.lineChart && this.$refs.lineChart.drawLine()
    }
  }
}
</script>

<style scoped>
.showMt {
  margin-top: 0px !important;
  width: 570px;
}

.arrow {
  background-size: 100% 100%;
  background-image: url("~@/static/screen1/arrow2.png");
  width: 50px;
  height: 35px;
  position: relative;
  top: -5px;
  background-repeat: no-repeat;
}

.linear-gradient {
  background: linear-gradient(
    to right,
    rgba(255, 0, 0, 0),
    rgba(255, 255, 255, 0.2)
  );
  margin: 12px 0 18px 0;
  box-sizing: content-box;
  background-clip: content-box;
}

.title1 {
  color: rgb(255, 255, 255);
  font-size: 20px;
  text-align: left;
  font-family: SourceHanSansSC-medium;
}
</style>
