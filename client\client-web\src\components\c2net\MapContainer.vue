<template>
  <div class="map-container">
    <div style="max-height: 800px; overflow: hidden">
      <!-- 中国地图组件 -->
      <china-map
        ref="chinaMap"
        :Count="count"
        :Stop="stopped"
        :taskResponse="taskResponse"
        :intervalSetting="intervalValue"
      ></china-map>

      <!-- 地图控制面板 -->
      <div class="control-panel-container">
        <map-control-panel
          :count="count"
          :total="total"
          :disabled="disabled"
          :is-playing="isPlaying"
          :show-button="showButton"
          :show-percentage="showPercentage"
          :interval-change-enabled="intervalChangeEnabled"
          :interval-value="intervalValue"
          @start="handleStart"
          @stop="handleStop"
          @continue="handleContinue"
          @progress-change="handleProgressChange"
          @interval-change="handleIntervalChange"
        ></map-control-panel>
      </div>

      <!-- 省份地图 -->
      <province-map2
        v-if="showProvinceMap2"
        style="position: absolute; top: 0px"
      ></province-map2>
      <province-map3
        v-if="showProvinceMap3"
        :Data="provinceData"
        style="position: absolute; top: 0px"
      ></province-map3>
    </div>
  </div>
</template>

<script>
import ChinaMap from '@/components/chinaMapDemo'
import ProvinceMap2 from '@/components/provinceMap5'
import ProvinceMap3 from '@/components/provinceMap3'
import MapControlPanel from './MapControlPanel.vue'

/**
 * 地图容器组件 - 包含中国地图、省份地图和地图控制
 */
export default {
  name: 'MapContainer',
  components: {
    ChinaMap,
    ProvinceMap2,
    ProvinceMap3,
    MapControlPanel
  },
  props: {
    /**
     * 当前计数
     */
    count: {
      type: Number,
      default: 0
    },
    /**
     * 任务总数
     */
    total: {
      type: Number,
      default: 0
    },
    /**
     * 是否停止
     */
    stopped: {
      type: Boolean,
      default: true
    },
    /**
     * 任务响应数据
     */
    taskResponse: {
      type: Object,
      default: null
    },
    /**
     * 当前时间间隔值
     */
    intervalValue: {
      type: Number,
      default: 24
    },
    /**
     * 是否禁用控制
     */
    disabled: {
      type: Boolean,
      default: false
    },
    /**
     * 是否正在播放
     */
    isPlaying: {
      type: Boolean,
      default: true
    },
    /**
     * 是否显示播放控制按钮
     */
    showButton: {
      type: Boolean,
      default: true
    },
    /**
     * 是否显示百分比进度条
     */
    showPercentage: {
      type: Boolean,
      default: true
    },
    /**
     * 是否允许更改时间间隔
     */
    intervalChangeEnabled: {
      type: Boolean,
      default: true
    },
    /**
     * 省份数据
     */
    provinceData: {
      type: Object,
      default: () => ({})
    },
    /**
     * 是否显示省份地图2
     */
    showProvinceMap2: {
      type: Boolean,
      default: false
    },
    /**
     * 是否显示省份地图3
     */
    showProvinceMap3: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    /**
     * 处理开始按钮点击
     */
    handleStart () {
      this.$emit('start')
    },

    /**
     * 处理停止按钮点击
     */
    handleStop () {
      this.$emit('stop')
    },

    /**
     * 处理继续按钮点击
     */
    handleContinue () {
      this.$emit('continue')
    },

    /**
     * 处理进度条变化
     */
    handleProgressChange (percentage) {
      this.$emit('progress-change', percentage)
    },

    /**
     * 处理时间间隔变化
     */
    handleIntervalChange (value) {
      this.$emit('interval-change', value)
    },

    /**
     * 获取任务详情
     */
    getJobDetail (id) {
      this.$refs.chinaMap && this.$refs.chinaMap.getJobDetail(id)
    }
  }
}
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.control-panel-container {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  z-index: 150;
}
</style>
