import axios from 'axios'
import NProgress from 'nprogress' // 导入NProgress
import 'nprogress/nprogress.css' // 导入NProgress CSS样式
// import { Message } from 'element-ui'
// import store from '@/store'
// import { removeToken } from '@/utils/auth'
// import router from '../router'

// 创建一个自定义的进度条样式
const requestProgress = NProgress.configure({
  parent: 'body',
  easing: 'ease',
  speed: 500,
  trickleSpeed: 200,
  showSpinner: false,
  minimum: 0.2
})

// 添加自定义样式
const style = document.createElement('style')
style.textContent = `
  #nprogress .bar {
    background: #f44336 !important;
    height: 4px !important;
    z-index: 10000 !important;
    position: fixed !important;
    top: 0 !important;
  }
  #nprogress .peg {
    box-shadow: 0 0 10px #f44336, 0 0 5px #f44336 !important;
  }
`
document.head.appendChild(style)

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 60000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // 开始进度条
    requestProgress.start()
    // do something before request is sent
    // if (store.getters.token) {
    //   config.headers['Authorization'] = 'Bearer ' + getToken()
    // }
    // eslint-disable-next-line no-undef
    return config
  },
  error => {
    // 发生错误时结束进度条
    requestProgress.done()
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */

  response => {
    // 请求成功时结束进度条
    requestProgress.done()
    const res = response.data
    res.data = response.data.payload
    // if (!response.data.success && (response.data.error.subcode === 16004 || response.data.error.subcode === 16010 || response.data.error.subcode === 16007)) {
    //   // setTimeout(function() {
    //   //   removeToken()
    //   //   router.replace({ path: '/' })
    //   // }, 1000)
    //   return res
    // } else {
    //   return res
    // }

    return res
  },
  error => {
    // 请求失败时结束进度条
    requestProgress.done()
    console.log('err' + error) // for debug
    // Message({
    //   message: error.message,
    //   type: 'error',
    //   duration: 5 * 1000
    // })
    return Promise.reject(error)
  }
)

export default service
